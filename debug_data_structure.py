#!/usr/bin/env python3
"""
Debug script to understand the actual data structure in RIO initial data
"""

import time
import json
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
import chromedriver_binary

def debug_character_data(character_name="Yonteaux", region="eu", server="drakthul"):
    """Debug the actual data structure for a character"""
    
    print(f"Debugging data structure for: {character_name}")
    
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    
    chromedriver_binary.add_chromedriver_to_path()
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        url = f"https://raider.io/characters/{region}/{server}/{character_name}"
        print(f"Loading: {url}")
        
        driver.get(url)
        
        WebDriverWait(driver, 10).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
        time.sleep(8)
        
        page_source = driver.page_source
        
        # Extract RIO data
        pattern = r'window\.__RIO_INITIAL_DATA\s*=\s*"(.*?)";'
        match = re.search(pattern, page_source, re.DOTALL)
        
        if match:
            json_string = match.group(1)
            
            try:
                data = json.loads(json_string)
            except json.JSONDecodeError:
                try:
                    unescaped = json_string.replace('\\"', '"').replace('\\\\', '\\')
                    data = json.loads(unescaped)
                except json.JSONDecodeError:
                    import codecs
                    decoded = codecs.decode(json_string, 'unicode_escape')
                    data = json.loads(decoded)
            
            print("Successfully extracted RIO data!")
            
            # Save full data for inspection
            with open(f'debug_full_data_{character_name}.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"Saved full data to debug_full_data_{character_name}.json")
            
            # Analyze character-specific data
            print("\n" + "="*60)
            print("CHARACTER DATA ANALYSIS")
            print("="*60)
            
            # Check characterDetails
            char_details = data.get('characterDetails', {})
            print(f"\ncharacterDetails keys: {list(char_details.keys()) if char_details else 'None'}")
            
            if char_details:
                print(f"Character name: {char_details.get('name', 'N/A')}")
                print(f"Character class: {char_details.get('class', 'N/A')}")
                print(f"Character level: {char_details.get('level', 'N/A')}")
                print(f"Character realm: {char_details.get('realm', 'N/A')}")
                
                # Look for M+ scores
                if 'mythicPlusScores' in char_details:
                    scores = char_details['mythicPlusScores']
                    print(f"Mythic+ scores found: {scores}")
                else:
                    print("No mythicPlusScores found in characterDetails")
                
                # Show all keys that might be M+ related
                mp_keys = [key for key in char_details.keys() if 'mythic' in key.lower() or 'plus' in key.lower() or 'score' in key.lower()]
                if mp_keys:
                    print(f"M+ related keys in characterDetails: {mp_keys}")
                    for key in mp_keys:
                        print(f"  {key}: {char_details[key]}")
            
            # Check characterMythicPlusRuns
            char_mp_runs = data.get('characterMythicPlusRuns', {})
            print(f"\ncharacterMythicPlusRuns keys: {list(char_mp_runs.keys()) if char_mp_runs else 'None'}")
            
            if char_mp_runs:
                runs = char_mp_runs.get('runs', [])
                print(f"Number of runs found: {len(runs)}")
                if runs:
                    print(f"First run sample: {runs[0]}")
            
            # Check characterMythicPlusProgress
            char_mp_progress = data.get('characterMythicPlusProgress', {})
            print(f"\ncharacterMythicPlusProgress keys: {list(char_mp_progress.keys()) if char_mp_progress else 'None'}")
            
            if char_mp_progress:
                progress = char_mp_progress.get('mythicPlusProgress', {})
                print(f"mythicPlusProgress keys: {list(progress.keys()) if progress else 'None'}")
                if progress:
                    print(f"Season ID: {progress.get('seasonId', 'N/A')}")
                    print(f"Total score: {progress.get('totalScore', 'N/A')}")
            
            # Check keystoneCharacterRankings
            char_rankings = data.get('keystoneCharacterRankings', {})
            print(f"\nkeystoneCharacterRankings keys: {list(char_rankings.keys()) if char_rankings else 'None'}")
            
            # Look for any other M+ related top-level keys
            mp_top_keys = [key for key in data.keys() if 'mythic' in key.lower() or 'keystone' in key.lower() or 'plus' in key.lower()]
            print(f"\nAll M+ related top-level keys: {mp_top_keys}")
            
            for key in mp_top_keys:
                value = data[key]
                if isinstance(value, dict):
                    print(f"\n{key} structure:")
                    print(f"  Keys: {list(value.keys())[:10]}...")
                    if value:
                        # Show a sample of the data
                        first_key = list(value.keys())[0]
                        first_value = value[first_key]
                        print(f"  Sample ({first_key}): {str(first_value)[:100]}...")
                elif isinstance(value, list):
                    print(f"\n{key}: List with {len(value)} items")
                    if value:
                        print(f"  First item: {str(value[0])[:100]}...")
                else:
                    print(f"\n{key}: {str(value)[:100]}...")
            
            return data
            
        else:
            print("Could not find RIO initial data")
            return None
            
    except Exception as e:
        print(f"Error debugging character data: {e}")
        return None
        
    finally:
        driver.quit()

if __name__ == "__main__":
    print("RIO Data Structure Debugger")
    print("=" * 40)
    debug_character_data()
