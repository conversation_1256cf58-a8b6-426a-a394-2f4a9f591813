#!/usr/bin/env python3
"""
Test using requests to see what we can get from raider.io without JavaScript
"""

import requests
import urllib.parse
from bs4 import BeautifulSoup

def test_requests_approach():
    """Test what we can get with simple HTTP requests"""
    print("Testing requests approach...")
    
    # Test basic raider.io access
    try:
        print("Testing basic raider.io access...")
        response = requests.get("https://raider.io", timeout=10)
        print(f"Status code: {response.status_code}")
        print(f"Content length: {len(response.text)} characters")
        
        if "raider" in response.text.lower():
            print("✓ Successfully accessed raider.io")
        else:
            print("✗ Unexpected content from raider.io")
            
    except Exception as e:
        print(f"✗ Error accessing raider.io: {e}")
        return False
    
    # Test character URL
    try:
        character_name = "<PERSON><PERSON><PERSON><PERSON>"
        encoded_name = urllib.parse.quote(character_name, safe='')
        character_url = f"https://raider.io/characters/eu/silvermoon/{encoded_name}"
        
        print(f"\nTesting character URL: {character_url}")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(character_url, headers=headers, timeout=15)
        print(f"Status code: {response.status_code}")
        print(f"Content length: {len(response.text)} characters")
        
        # Parse with BeautifulSoup
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Look for any character-related content
        title = soup.find('title')
        if title:
            print(f"Page title: {title.get_text()}")
        
        # Look for specific elements
        character_elements = soup.find_all(text=lambda text: text and 'character' in text.lower())
        if character_elements:
            print(f"Found {len(character_elements)} character-related text elements")
        
        # Check for JavaScript requirement message
        if "javascript" in response.text.lower():
            print("⚠ Page requires JavaScript - Selenium will be needed")
        
        # Save a sample of the HTML for inspection
        with open("sample_page.html", "w", encoding="utf-8") as f:
            f.write(response.text)
        print("✓ Saved page content to sample_page.html for inspection")
        
        return True
        
    except Exception as e:
        print(f"✗ Error accessing character page: {e}")
        return False

if __name__ == "__main__":
    print("Raider.IO Requests Test")
    print("=" * 30)
    test_requests_approach()
