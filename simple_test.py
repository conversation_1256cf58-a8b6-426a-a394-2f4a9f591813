#!/usr/bin/env python3
"""
Simple test to verify Selenium setup and basic raider.io access
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager

def test_selenium_setup():
    """Test basic Selenium setup"""
    print("Testing Selenium setup...")
    
    try:
        # Setup Chrome options
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # Run in background
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        
        print("Setting up Chrome driver...")
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        print("Chrome driver setup successful!")
        
        # Test basic navigation
        print("Testing navigation to raider.io...")
        driver.get("https://raider.io")
        
        print(f"Page title: {driver.title}")
        print(f"Current URL: {driver.current_url}")
        
        # Test the specific character URL
        test_url = "https://raider.io/characters/eu/silvermoon/C%C3%A9lesti"
        print(f"Testing character URL: {test_url}")
        
        driver.get(test_url)
        time.sleep(5)  # Wait for page to load
        
        print(f"Character page title: {driver.title}")
        print(f"Character page URL: {driver.current_url}")
        
        # Check if we can find any content
        page_source_length = len(driver.page_source)
        print(f"Page source length: {page_source_length} characters")
        
        if "character" in driver.page_source.lower():
            print("✓ Found character-related content on the page")
        else:
            print("✗ No character-related content found")
        
        driver.quit()
        print("✓ Test completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ Error during test: {e}")
        return False

if __name__ == "__main__":
    print("Simple Raider.IO Scraper Test")
    print("=" * 40)
    test_selenium_setup()
