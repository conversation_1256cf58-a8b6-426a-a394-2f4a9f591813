#!/usr/bin/env python3
"""
Debug script to understand the actual run count data structure
"""

import time
import json
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
import chromedriver_binary

def debug_run_count(character_name="seedhy", region="eu", server="stormscale"):
    """Debug the actual run count data structure"""
    
    print(f"Debugging run count for: {character_name}")
    
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    
    chromedriver_binary.add_chromedriver_to_path()
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        url = f"https://raider.io/characters/{region}/{server}/{character_name}"
        print(f"Loading: {url}")
        
        driver.get(url)
        
        WebDriverWait(driver, 10).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
        time.sleep(8)
        
        page_source = driver.page_source
        
        # Extract RIO data
        pattern = r'window\.__RIO_INITIAL_DATA\s*=\s*"(.*?)";'
        match = re.search(pattern, page_source, re.DOTALL)
        
        if match:
            json_string = match.group(1)
            
            try:
                data = json.loads(json_string)
            except json.JSONDecodeError:
                try:
                    unescaped = json_string.replace('\\"', '"').replace('\\\\', '\\')
                    data = json.loads(unescaped)
                except json.JSONDecodeError:
                    import codecs
                    decoded = codecs.decode(json_string, 'unicode_escape')
                    data = json.loads(decoded)
            
            print("Successfully extracted RIO data!")
            
            # Look for all possible sources of run data
            print("\n" + "="*60)
            print("ANALYZING RUN COUNT DATA SOURCES")
            print("="*60)
            
            # 1. Check keystoneRuns
            keystone_runs = data.get('keystoneRuns', {})
            if keystone_runs and 'dungeonRuns' in keystone_runs:
                print(f"\n1. keystoneRuns.dungeonRuns:")
                dungeon_runs = keystone_runs['dungeonRuns']
                total_keystone_runs = 0
                
                for category_key, runs_list in dungeon_runs.items():
                    if isinstance(runs_list, list):
                        print(f"   {category_key}: {len(runs_list)} runs")
                        total_keystone_runs += len(runs_list)
                        
                        # Show sample run data
                        if runs_list:
                            sample_run = runs_list[0]
                            if 'summary' in sample_run:
                                summary = sample_run['summary']
                                print(f"     Sample: {summary.get('dungeon', {}).get('name', 'Unknown')} +{summary.get('mythicLevel', 0)}")
                
                print(f"   TOTAL from keystoneRuns: {total_keystone_runs} runs")
            
            # 2. Check characterMythicPlusProgress
            char_mp_progress = data.get('characterMythicPlusProgress', {})
            if char_mp_progress and 'mythicPlusProgress' in char_mp_progress:
                print(f"\n2. characterMythicPlusProgress:")
                progress_data = char_mp_progress['mythicPlusProgress']
                
                for season_key, season_data in progress_data.items():
                    if 'season-tww' in season_key and season_data:
                        print(f"   Season {season_key}:")
                        
                        # Check mythicPlusScores runs
                        if 'mythicPlusScores' in season_data:
                            mp_scores = season_data['mythicPlusScores']
                            
                            for role, role_data in mp_scores.items():
                                if isinstance(role_data, dict) and 'runs' in role_data:
                                    runs = role_data['runs']
                                    print(f"     {role} role: {len(runs)} best runs")
                                    
                                    # Check for rawRuns or alternateRuns
                                    if 'rawRuns' in role_data:
                                        raw_runs = role_data['rawRuns']
                                        print(f"     {role} rawRuns: {len(raw_runs)} runs")
                                    
                                    if 'alternateRuns' in role_data:
                                        alt_runs = role_data['alternateRuns']
                                        print(f"     {role} alternateRuns: {len(alt_runs)} runs")
                                    
                                    if 'rawAlternateRuns' in role_data:
                                        raw_alt_runs = role_data['rawAlternateRuns']
                                        print(f"     {role} rawAlternateRuns: {len(raw_alt_runs)} runs")
            
            # 3. Check characterMythicPlusRuns
            char_mp_runs = data.get('characterMythicPlusRuns', {})
            if char_mp_runs:
                print(f"\n3. characterMythicPlusRuns:")
                
                if 'dungeons' in char_mp_runs:
                    dungeons = char_mp_runs['dungeons']
                    total_char_runs = 0
                    
                    for dungeon in dungeons:
                        if 'runs' in dungeon:
                            runs = dungeon['runs']
                            dungeon_name = dungeon.get('dungeon', {}).get('name', 'Unknown')
                            print(f"   {dungeon_name}: {len(runs)} runs")
                            total_char_runs += len(runs)
                    
                    print(f"   TOTAL from characterMythicPlusRuns: {total_char_runs} runs")
            
            # 4. Look for any other run-related data
            print(f"\n4. Other potential run data:")
            
            # Search for any keys containing 'run' or 'mythic'
            def find_run_data(obj, path="", max_depth=3):
                if max_depth <= 0:
                    return []
                
                results = []
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        current_path = f"{path}.{key}" if path else key
                        
                        # Check if key suggests run data
                        if any(term in key.lower() for term in ['run', 'mythic', 'keystone']) and isinstance(value, list):
                            results.append({
                                'path': current_path,
                                'count': len(value),
                                'sample': str(value[0])[:100] if value else 'Empty'
                            })
                        
                        # Recurse
                        if isinstance(value, (dict, list)):
                            results.extend(find_run_data(value, current_path, max_depth - 1))
                
                elif isinstance(obj, list) and obj:
                    # Check if this looks like run data
                    if isinstance(obj[0], dict) and any(key in str(obj[0]).lower() for key in ['mythic', 'dungeon', 'keystone']):
                        results.append({
                            'path': path,
                            'count': len(obj),
                            'sample': str(obj[0])[:100]
                        })
                
                return results
            
            run_data_sources = find_run_data(data)
            for source in run_data_sources[:10]:  # Show first 10
                print(f"   {source['path']}: {source['count']} items")
                print(f"     Sample: {source['sample']}")
            
            # 5. Check for any statistics or summary data
            print(f"\n5. Looking for run statistics:")
            
            # Look for any numeric values that might represent total runs
            def find_numbers(obj, path="", target_range=(10, 1000)):
                results = []
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        current_path = f"{path}.{key}" if path else key
                        
                        if isinstance(value, (int, float)) and target_range[0] <= value <= target_range[1]:
                            if any(term in key.lower() for term in ['run', 'count', 'total', 'completed']):
                                results.append({
                                    'path': current_path,
                                    'value': value
                                })
                        
                        elif isinstance(value, dict):
                            results.extend(find_numbers(value, current_path, target_range))
                
                return results
            
            potential_counts = find_numbers(data)
            for count_data in potential_counts[:10]:
                print(f"   {count_data['path']}: {count_data['value']}")
            
            return data
            
        else:
            print("Could not find RIO initial data")
            return None
            
    except Exception as e:
        print(f"Error debugging run count: {e}")
        return None
        
    finally:
        driver.quit()

if __name__ == "__main__":
    print("Run Count Debugger")
    print("=" * 30)
    debug_run_count()
