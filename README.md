# Raider.IO Character Scraper

A Python-based web scraper for extracting character information from raider.io. This scraper handles special characters in character names and can extract various types of character data including Mythic+ scores, raid progress, and basic character information.

## Features

- ✅ Handles special characters in character names (like <PERSON><PERSON><PERSON><PERSON>)
- ✅ Extracts Mythic+ scores and best runs
- ✅ Extracts raid progression data
- ✅ Extracts basic character information
- ✅ Supports batch processing from a character list file
- ✅ Rate limiting to be respectful to the website
- ✅ Multiple output formats (JSON, CSV)
- ✅ Configurable settings
- ✅ Error handling and logging

## Requirements

- Python 3.7+
- Chrome browser (for Selenium)
- Internet connection

## Quick Start

1. **Setup the environment:**
   ```bash
   python setup.py
   ```

2. **Edit the character list:**
   Open `characters.txt` and add the character names you want to scrape (one per line):
   ```
   Célesti
   Anothername
   Testcharacter
   ```

3. **Run the scraper:**
   ```bash
   python scraper.py
   ```

4. **Check the results:**
   The scraped data will be saved as JSON and/or CSV files with timestamps.

## Configuration

Edit `config.py` to customize the scraper behavior:

- **Region and Server:** Change `DEFAULT_REGION` and `DEFAULT_SERVER`
- **Browser Settings:** Set `HEADLESS_BROWSER = False` to see the browser window
- **Rate Limiting:** Adjust `REQUEST_DELAY` (seconds between requests)
- **Data Extraction:** Choose what data to extract in `EXTRACT_DATA`
- **Output Format:** Set to "json", "csv", or "both"

## File Structure

```
├── scraper.py          # Main scraper script
├── config.py           # Configuration settings
├── characters.txt      # List of characters to scrape
├── requirements.txt    # Python dependencies
├── setup.py           # Setup script
└── README.md          # This file
```

## Character Name Format

The scraper automatically handles special characters. Just add character names to `characters.txt` as they appear in-game:

```
Célesti
Tëstñamé
Normalname
```

The scraper will properly URL-encode these names for the raider.io URLs.

## URL Structure

The scraper builds URLs in this format:
```
https://raider.io/characters/{region}/{server}/{encoded_character_name}
```

Example:
- Character: `Célesti`
- Region: `eu`
- Server: `silvermoon`
- URL: `https://raider.io/characters/eu/silvermoon/C%C3%A9lesti`

## Output Data

The scraper extracts the following data (configurable):

### Basic Information
- Character name
- Class and specialization
- Level
- Server information

### Mythic+ Data
- Raider.IO score
- Best dungeon runs
- Key levels

### Raid Progress
- Current tier progression
- Boss kills by difficulty

## Troubleshooting

### Common Issues

1. **"Chrome driver not found"**
   - Make sure Chrome browser is installed
   - The script will automatically download the Chrome driver

2. **"Page load timeout"**
   - Check your internet connection
   - Increase `PAGE_LOAD_TIMEOUT` in config.py

3. **"Character not found"**
   - Verify the character name spelling
   - Check if the character exists on the specified server/region

4. **Rate limiting errors**
   - Increase `REQUEST_DELAY` in config.py
   - The scraper already includes reasonable delays

### Debug Mode

To see the browser window and debug issues:
1. Set `HEADLESS_BROWSER = False` in `config.py`
2. Run the scraper again

## Ethical Usage

This scraper is designed to be respectful to raider.io:
- Includes rate limiting between requests
- Uses reasonable timeouts
- Doesn't overwhelm the server

Please use responsibly and consider the website's terms of service.

## Example Output

```json
{
  "character_name": "Célesti",
  "url": "https://raider.io/characters/eu/silvermoon/C%C3%A9lesti",
  "scraped_at": "2024-01-15 14:30:22",
  "success": true,
  "basic_info": {
    "name": "Célesti",
    "class_spec": "Frost Mage",
    "level": "80",
    "server": "Silvermoon-EU"
  },
  "mythic_plus": {
    "rio_score": "2847",
    "best_runs": [
      {
        "dungeon": "The Necrotic Wake",
        "key_level": "+15"
      }
    ]
  },
  "raid_progress": {
    "Nerub-ar Palace": "8/8 Heroic"
  }
}
```

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify your Python and Chrome versions
3. Check the console output for error messages

## License

This project is for educational purposes. Please respect raider.io's terms of service and use responsibly.
