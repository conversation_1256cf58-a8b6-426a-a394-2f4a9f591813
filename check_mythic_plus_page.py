#!/usr/bin/env python3
"""
Check if there's a dedicated M+ page with complete run history
"""

import time
import json
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
import chromedriver_binary

def check_mythic_plus_page(character_name="seedhy", region="eu", server="stormscale"):
    """Check different M+ page URLs for complete run history"""
    
    print(f"Checking M+ pages for: {character_name}")
    
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    
    chromedriver_binary.add_chromedriver_to_path()
    driver = webdriver.Chrome(options=chrome_options)
    
    # Try different URL patterns that might contain complete run history
    url_patterns = [
        f"https://raider.io/characters/{region}/{server}/{character_name}/mythic-plus",
        f"https://raider.io/characters/{region}/{server}/{character_name}/mythic-plus/season-tww-2",
        f"https://raider.io/characters/{region}/{server}/{character_name}/runs",
        f"https://raider.io/characters/{region}/{server}/{character_name}/mythic-plus-runs",
        f"https://raider.io/characters/{region}/{server}/{character_name}#mythic-plus",
    ]
    
    try:
        for i, url in enumerate(url_patterns, 1):
            print(f"\n{i}. Trying URL: {url}")
            
            try:
                driver.get(url)
                
                WebDriverWait(driver, 10).until(
                    lambda d: d.execute_script("return document.readyState") == "complete"
                )
                time.sleep(5)
                
                page_source = driver.page_source
                
                # Check if this page has more run data
                print(f"   Page length: {len(page_source)} characters")
                
                # Look for RIO data
                pattern = r'window\.__RIO_INITIAL_DATA\s*=\s*"(.*?)";'
                match = re.search(pattern, page_source, re.DOTALL)
                
                if match:
                    json_string = match.group(1)
                    
                    try:
                        data = json.loads(json_string)
                    except json.JSONDecodeError:
                        try:
                            unescaped = json_string.replace('\\"', '"').replace('\\\\', '\\')
                            data = json.loads(unescaped)
                        except json.JSONDecodeError:
                            import codecs
                            decoded = codecs.decode(json_string, 'unicode_escape')
                            data = json.loads(decoded)
                    
                    # Check keystoneRuns data
                    keystone_runs = data.get('keystoneRuns', {})
                    if keystone_runs and 'dungeonRuns' in keystone_runs:
                        dungeon_runs = keystone_runs['dungeonRuns']
                        total_runs = 0
                        
                        for category_key, runs_list in dungeon_runs.items():
                            if isinstance(runs_list, list):
                                total_runs += len(runs_list)
                                print(f"   {category_key}: {len(runs_list)} runs")
                        
                        print(f"   TOTAL RUNS FOUND: {total_runs}")
                        
                        # Check for any additional data structures
                        print(f"   keystoneRuns keys: {list(keystone_runs.keys())}")
                        
                        # Look for pagination or loading info
                        if 'pagination' in keystone_runs:
                            print(f"   Pagination: {keystone_runs['pagination']}")
                        
                        if 'hasMore' in keystone_runs:
                            print(f"   Has more: {keystone_runs['hasMore']}")
                        
                        if 'totalCount' in keystone_runs:
                            print(f"   Total count: {keystone_runs['totalCount']}")
                        
                        # Check for any API endpoints or next page URLs
                        for key, value in keystone_runs.items():
                            if isinstance(value, str) and ('http' in value or 'api' in value):
                                print(f"   Found URL in {key}: {value}")
                    
                    else:
                        print("   No keystoneRuns data found")
                    
                    # Check for other potential run data sources
                    other_run_sources = []
                    for key in data.keys():
                        if 'run' in key.lower() or 'mythic' in key.lower():
                            other_run_sources.append(key)
                    
                    if other_run_sources:
                        print(f"   Other potential run sources: {other_run_sources}")
                
                else:
                    print("   No RIO initial data found")
                
                # Check if page title or content suggests this is the right page
                if 'mythic' in page_source.lower() and 'plus' in page_source.lower():
                    print("   ✓ Page contains M+ content")
                else:
                    print("   ✗ Page doesn't seem to contain M+ content")
                
            except Exception as e:
                print(f"   Error loading URL: {e}")
        
        # Also try the API approach - check if there are direct API endpoints
        print(f"\n" + "="*60)
        print("CHECKING FOR API ENDPOINTS")
        print("="*60)
        
        # Try potential API endpoints
        api_patterns = [
            f"https://raider.io/api/v1/characters/profile?region={region}&realm={server}&name={character_name}&fields=mythic_plus_runs",
            f"https://raider.io/api/v1/characters/mythic-plus-runs?region={region}&realm={server}&name={character_name}",
        ]
        
        for i, api_url in enumerate(api_patterns, 1):
            print(f"\n{i}. Trying API: {api_url}")
            
            try:
                driver.get(api_url)
                time.sleep(3)
                
                page_source = driver.page_source
                
                if 'json' in page_source.lower() or '{' in page_source:
                    print(f"   Got JSON response: {len(page_source)} characters")
                    print(f"   Preview: {page_source[:200]}...")
                    
                    # Try to parse as JSON
                    try:
                        api_data = json.loads(page_source)
                        if 'mythic_plus_runs' in api_data:
                            runs = api_data['mythic_plus_runs']
                            print(f"   Found {len(runs)} runs in API response!")
                        else:
                            print(f"   API response keys: {list(api_data.keys()) if isinstance(api_data, dict) else 'Not a dict'}")
                    except:
                        print("   Could not parse as JSON")
                else:
                    print("   No JSON response")
                    
            except Exception as e:
                print(f"   API error: {e}")
        
    except Exception as e:
        print(f"Error checking M+ pages: {e}")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    print("Mythic+ Page Checker")
    print("=" * 30)
    check_mythic_plus_page()
