#!/usr/bin/env python3
"""
Setup script for Raider.IO scraper
This script installs the required dependencies and sets up the environment.
"""

import subprocess
import sys
import os

def install_requirements():
    """Install Python requirements"""
    print("Installing Python requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Requirements installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Error installing requirements: {e}")
        return False

def check_character_file():
    """Check if character file exists and create example if not"""
    if not os.path.exists("characters.txt"):
        print("Creating example characters.txt file...")
        with open("characters.txt", "w", encoding="utf-8") as f:
            f.write("# Add character names here, one per line\n")
            f.write("# Example characters (replace with your own):\n")
            f.write("<PERSON><PERSON>lesti\n")
            f.write("Testcharacter\n")
        print("✓ Example characters.txt created!")
    else:
        print("✓ characters.txt already exists!")

def main():
    """Main setup function"""
    print("Raider.IO Scraper Setup")
    print("=" * 30)
    
    # Install requirements
    if not install_requirements():
        print("Setup failed. Please check the error messages above.")
        return
    
    # Check character file
    check_character_file()
    
    print("\n" + "=" * 30)
    print("Setup complete!")
    print("\nNext steps:")
    print("1. Edit 'characters.txt' to add the character names you want to scrape")
    print("2. Optionally edit 'config.py' to customize settings")
    print("3. Run the scraper with: python scraper.py")
    print("\nNote: Make sure you have Chrome browser installed for Selenium to work.")

if __name__ == "__main__":
    main()
