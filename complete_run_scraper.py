#!/usr/bin/env python3
"""
Complete run scraper that attempts to load ALL runs for a character
"""

import time
import json
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
import chromedriver_binary

def complete_run_scraper(character_name="seedhy", region="eu", server="stormscale"):
    """Attempt to load and scrape ALL runs for a character"""
    
    print(f"Complete run scraping for: {character_name}")
    
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    chromedriver_binary.add_chromedriver_to_path()
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Start with the M+ specific page
        url = f"https://raider.io/characters/{region}/{server}/{character_name}/mythic-plus"
        print(f"Loading M+ page: {url}")
        
        driver.get(url)
        
        WebDriverWait(driver, 15).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
        time.sleep(5)
        
        print("Page loaded. Attempting to load all runs...")
        
        # Strategy 1: Look for "Load More" or "Show All" buttons
        load_more_selectors = [
            "button:contains('Load More')",
            "button:contains('Show More')",
            "button:contains('View All')",
            "button:contains('See All')",
            ".load-more",
            ".show-more",
            ".view-all",
            "[data-testid*='load']",
            "[data-testid*='more']"
        ]
        
        for selector in load_more_selectors:
            try:
                if "contains" in selector:
                    # Use XPath for text-based selectors
                    button_text = selector.split("'")[1]
                    xpath_selector = f"//button[contains(text(), '{button_text}')]"
                    buttons = driver.find_elements(By.XPATH, xpath_selector)
                else:
                    buttons = driver.find_elements(By.CSS_SELECTOR, selector)
                
                if buttons:
                    print(f"Found {len(buttons)} buttons with selector: {selector}")
                    for button in buttons:
                        try:
                            if button.is_displayed() and button.is_enabled():
                                print(f"Clicking button: {button.text}")
                                driver.execute_script("arguments[0].scrollIntoView(true);", button)
                                time.sleep(1)
                                button.click()
                                time.sleep(3)  # Wait for content to load
                        except Exception as e:
                            print(f"Error clicking button: {e}")
            except:
                continue
        
        # Strategy 2: Infinite scroll to load more content
        print("Attempting infinite scroll...")
        
        last_height = driver.execute_script("return document.body.scrollHeight")
        scroll_attempts = 0
        max_scrolls = 20  # Limit to prevent infinite loops
        
        while scroll_attempts < max_scrolls:
            # Scroll to bottom
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            
            # Check if new content loaded
            new_height = driver.execute_script("return document.body.scrollHeight")
            if new_height == last_height:
                print(f"No new content after scroll {scroll_attempts + 1}")
                break
            else:
                print(f"Scroll {scroll_attempts + 1}: Page height increased from {last_height} to {new_height}")
                last_height = new_height
            
            scroll_attempts += 1
        
        # Strategy 3: Look for and expand dungeon table rows
        print("Looking for expandable dungeon rows...")
        
        # Look for table rows that might be expandable
        table_selectors = [
            "table tbody tr",
            ".table-row",
            ".dungeon-row",
            "[data-dungeon]",
            "tr[role='button']",
            "tr[aria-expanded]",
            ".collapsible-row"
        ]
        
        expandable_rows = []
        for selector in table_selectors:
            try:
                rows = driver.find_elements(By.CSS_SELECTOR, selector)
                if rows:
                    print(f"Found {len(rows)} rows with selector: {selector}")
                    for row in rows:
                        # Check if row contains dungeon-related text
                        try:
                            row_text = row.text.lower()
                            if any(keyword in row_text for keyword in ['dungeon', 'mythic', '+', 'keystone']):
                                expandable_rows.append(row)
                        except:
                            pass
            except:
                continue
        
        # Remove duplicates
        unique_rows = list(set(expandable_rows))
        print(f"Found {len(unique_rows)} unique expandable rows")
        
        # Try to expand rows
        expanded_count = 0
        for i, row in enumerate(unique_rows[:50]):  # Limit to prevent issues
            try:
                if row.is_displayed():
                    # Try clicking the row
                    driver.execute_script("arguments[0].scrollIntoView(true);", row)
                    time.sleep(0.5)
                    
                    try:
                        row.click()
                        expanded_count += 1
                        print(f"Expanded row {i+1}")
                        time.sleep(1)
                    except:
                        # Try double-click
                        try:
                            ActionChains(driver).double_click(row).perform()
                            expanded_count += 1
                            print(f"Double-clicked row {i+1}")
                            time.sleep(1)
                        except:
                            pass
            except:
                pass
        
        print(f"Expanded {expanded_count} rows")
        
        # Strategy 4: Look for specific dungeon expansion buttons
        print("Looking for dungeon expansion buttons...")
        
        expansion_selectors = [
            ".expand-icon",
            ".collapse-icon",
            "[aria-label*='expand']",
            "[aria-label*='collapse']",
            ".fa-chevron-down",
            ".fa-chevron-right",
            ".fa-plus",
            ".fa-minus",
            "button[aria-expanded='false']"
        ]
        
        for selector in expansion_selectors:
            try:
                buttons = driver.find_elements(By.CSS_SELECTOR, selector)
                if buttons:
                    print(f"Found {len(buttons)} expansion buttons with selector: {selector}")
                    for button in buttons[:20]:  # Limit to first 20
                        try:
                            if button.is_displayed() and button.is_enabled():
                                driver.execute_script("arguments[0].scrollIntoView(true);", button)
                                time.sleep(0.5)
                                button.click()
                                time.sleep(1)
                                print(f"Clicked expansion button")
                        except:
                            pass
            except:
                continue
        
        # Wait for all content to load
        print("Waiting for all content to load...")
        time.sleep(10)
        
        # Extract final run count
        print("Extracting final run data...")
        
        page_source = driver.page_source
        pattern = r'window\.__RIO_INITIAL_DATA\s*=\s*"(.*?)";'
        match = re.search(pattern, page_source, re.DOTALL)
        
        if match:
            json_string = match.group(1)
            
            try:
                data = json.loads(json_string)
            except json.JSONDecodeError:
                try:
                    unescaped = json_string.replace('\\"', '"').replace('\\\\', '\\')
                    data = json.loads(unescaped)
                except json.JSONDecodeError:
                    import codecs
                    decoded = codecs.decode(json_string, 'unicode_escape')
                    data = json.loads(decoded)
            
            # Check keystoneRuns data
            keystone_runs = data.get('keystoneRuns', {})
            if keystone_runs and 'dungeonRuns' in keystone_runs:
                dungeon_runs = keystone_runs['dungeonRuns']
                total_runs = 0
                
                print(f"\nFinal run count by category:")
                for category_key, runs_list in dungeon_runs.items():
                    if isinstance(runs_list, list):
                        total_runs += len(runs_list)
                        print(f"   {category_key}: {len(runs_list)} runs")
                
                print(f"   TOTAL RUNS FOUND: {total_runs}")
                
                # Save the complete data
                with open(f'complete_runs_data_{character_name}.json', 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                print(f"Saved complete data to complete_runs_data_{character_name}.json")
                
                # Extract unique runs
                all_runs = []
                seen_runs = set()
                
                for category_key, runs_list in dungeon_runs.items():
                    if isinstance(runs_list, list):
                        for run in runs_list:
                            summary = run.get('summary', {})
                            if summary:
                                # Create unique identifier
                                run_id = summary.get('keystone_run_id', '')
                                if not run_id:
                                    run_id = f"{summary.get('dungeon', {}).get('name', '')}_{summary.get('mythic_level', 0)}_{summary.get('completed_at', '')}_{run.get('score', 0)}"
                                
                                if run_id not in seen_runs:
                                    seen_runs.add(run_id)
                                    all_runs.append({
                                        'dungeon': summary.get('dungeon', {}).get('name', ''),
                                        'level': summary.get('mythic_level', 0),
                                        'score': run.get('score', 0),
                                        'completed_at': summary.get('completed_at', ''),
                                        'category': category_key
                                    })
                
                print(f"UNIQUE RUNS AFTER DEDUPLICATION: {len(all_runs)}")
                
                # Save runs summary
                with open(f'complete_runs_summary_{character_name}.json', 'w', encoding='utf-8') as f:
                    json.dump(all_runs, f, indent=2, ensure_ascii=False)
                
                return len(all_runs)
            
            else:
                print("No keystoneRuns data found")
                return 0
        
        else:
            print("Could not extract RIO data")
            return 0
            
    except Exception as e:
        print(f"Error in complete run scraper: {e}")
        return 0
        
    finally:
        driver.quit()

if __name__ == "__main__":
    print("Complete Run Scraper")
    print("=" * 30)
    
    total_runs = complete_run_scraper()
    print(f"\nFINAL RESULT: Found {total_runs} unique runs")
    
    if total_runs < 50:  # If we still don't have many runs
        print("\nNote: If this character has more runs than found, they might be:")
        print("1. From previous seasons (not current season)")
        print("2. Require additional API calls not accessible through web scraping")
        print("3. Hidden behind authentication or rate limiting")
        print("4. Stored in a different data structure we haven't found yet")
