#!/usr/bin/env python3
"""
Alternative scraper with better Chrome driver handling
"""

import time
import sys
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def setup_chrome_driver():
    """Setup Chrome driver with multiple fallback options"""
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-plugins")
    chrome_options.add_argument("--disable-images")
    
    # Try different approaches
    approaches = [
        "webdriver_manager",
        "chromedriver_binary", 
        "system_path"
    ]
    
    for approach in approaches:
        try:
            print(f"Trying approach: {approach}")
            
            if approach == "webdriver_manager":
                from webdriver_manager.chrome import ChromeDriverManager
                service = Service(ChromeDriverManager().install())
                driver = webdriver.Chrome(service=service, options=chrome_options)
                
            elif approach == "chromedriver_binary":
                import chromedriver_binary
                chromedriver_binary.add_chromedriver_to_path()
                driver = webdriver.Chrome(options=chrome_options)
                
            elif approach == "system_path":
                driver = webdriver.Chrome(options=chrome_options)
            
            # Test the driver
            driver.get("https://www.google.com")
            print(f"✓ Successfully created driver using {approach}")
            return driver
            
        except Exception as e:
            print(f"✗ Failed with {approach}: {e}")
            continue
    
    print("✗ All approaches failed")
    return None

def test_raider_io_access():
    """Test accessing raider.io with the driver"""
    driver = setup_chrome_driver()
    if not driver:
        return False
    
    try:
        print("Testing raider.io access...")
        driver.get("https://raider.io/characters/eu/silvermoon/C%C3%A9lesti")
        
        # Wait for page to load
        WebDriverWait(driver, 10).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
        
        print(f"Page title: {driver.title}")
        print(f"URL: {driver.current_url}")
        
        # Try to find character data
        time.sleep(5)  # Wait for JavaScript to load
        
        page_source = driver.page_source
        if len(page_source) > 100000:  # Reasonable size for loaded page
            print("✓ Page appears to be fully loaded")
            
            # Look for character-specific content
            if "character" in page_source.lower():
                print("✓ Found character content")
            else:
                print("⚠ No character content found yet")
                
        else:
            print("⚠ Page may not be fully loaded")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing raider.io: {e}")
        return False
        
    finally:
        driver.quit()

if __name__ == "__main__":
    print("Alternative Chrome Driver Test")
    print("=" * 40)
    test_raider_io_access()
