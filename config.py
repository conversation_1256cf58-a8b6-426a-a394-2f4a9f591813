"""
Configuration settings for the Raider.IO scraper
"""

# Default region and server
DEFAULT_REGION = "eu"
DEFAULT_SERVER = "stormscale"

# Base URL for raider.io
BASE_URL = "https://raider.io"

# Selenium settings
SELENIUM_TIMEOUT = 10
PAGE_LOAD_TIMEOUT = 30

# Rate limiting (seconds between requests)
REQUEST_DELAY = 2

# Output settings
OUTPUT_FORMAT = "both"  # Options: "json", "csv", "both"
OUTPUT_FILENAME = "scraped_characters"

# Browser settings
HEADLESS_BROWSER = True  # Set to False to see browser window
BROWSER_TYPE = "chrome"  # Options: "chrome", "firefox"

# Character file settings
CHARACTER_FILE = "characters.txt"

# Data to extract (customize based on what you want to scrape)
EXTRACT_DATA = {
    "basic_info": True,      # Name, class, spec, level
    "mythic_plus": True,     # M+ score, best runs
    "raid_progress": True,   # Current tier progress
    "gear": True,           # Item level, notable items
    "achievements": False,   # Achievement points, notable achievements
}
