#!/usr/bin/env python3
"""
Raider.IO Character Scraper

This script scrapes character information from raider.io for a list of characters.
It handles special characters in names and extracts various character data.
"""

import json
import csv
import time
import urllib.parse
from typing import Dict, List, Optional
from pathlib import Path

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import chromedriver_binary  # This will add chromedriver to PATH
from bs4 import BeautifulSoup
import pandas as pd

import config


class RaiderIOScraper:
    """Main scraper class for raider.io character data"""
    
    def __init__(self):
        self.driver = None
        self.scraped_data = []
        
    def setup_driver(self):
        """Initialize the Selenium WebDriver"""
        print("Setting up browser driver...")

        chrome_options = Options()
        if config.HEADLESS_BROWSER:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")

        # Use chromedriver_binary which automatically adds the correct driver to PATH
        chromedriver_binary.add_chromedriver_to_path()
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.set_page_load_timeout(config.PAGE_LOAD_TIMEOUT)

        print("Browser driver setup complete.")
    
    def close_driver(self):
        """Close the WebDriver"""
        if self.driver:
            self.driver.quit()
            print("Browser driver closed.")
    
    def encode_character_name(self, name: str) -> str:
        """Properly encode character name for URL"""
        return urllib.parse.quote(name, safe='')
    
    def build_character_url(self, character_name: str, region: str = None, server: str = None) -> str:
        """Build the full URL for a character"""
        region = region or config.DEFAULT_REGION
        server = server or config.DEFAULT_SERVER
        encoded_name = self.encode_character_name(character_name)
        
        url = f"{config.BASE_URL}/characters/{region}/{server}/{encoded_name}"
        return url
    
    def wait_for_page_load(self):
        """Wait for the page to fully load"""
        try:
            # Wait for the page to be ready
            WebDriverWait(self.driver, config.SELENIUM_TIMEOUT).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )

            # Give extra time for JavaScript to load content
            time.sleep(3)

            # Check if we have substantial content (indicating the page loaded)
            page_source = self.driver.page_source
            if len(page_source) > 50000:  # Reasonable size for a loaded page
                return True
            else:
                print("Page content seems too small, may not be fully loaded")
                return False

        except TimeoutException:
            print("Timeout waiting for page to load")
            return False
    
    def extract_basic_info(self, soup: BeautifulSoup) -> Dict:
        """Extract basic character information"""
        basic_info = {}

        try:
            # Try to find character name in various possible locations
            name_selectors = [
                "h1.character-name",
                ".character-name",
                "h1",
                "[data-testid='character-name']"
            ]

            for selector in name_selectors:
                name_element = soup.select_one(selector)
                if name_element:
                    basic_info["name"] = name_element.get_text(strip=True)
                    break

            # Try to find class/spec information
            class_selectors = [
                ".character-class-spec",
                ".character-class",
                "[data-testid='character-class']"
            ]

            for selector in class_selectors:
                class_element = soup.select_one(selector)
                if class_element:
                    basic_info["class_spec"] = class_element.get_text(strip=True)
                    break

            # Look for any text that might contain character info
            page_text = soup.get_text()
            if "level" in page_text.lower():
                basic_info["page_contains_level"] = True
            if "class" in page_text.lower():
                basic_info["page_contains_class"] = True

        except Exception as e:
            print(f"Error extracting basic info: {e}")

        return basic_info
    
    def extract_mythic_plus_data(self, soup: BeautifulSoup) -> Dict:
        """Extract Mythic+ related data"""
        mythic_plus_data = {}

        try:
            # Look for M+ score in various formats
            score_selectors = [
                ".rio-score",
                "[data-testid='rio-score']",
                ".mythic-plus-score",
                ".score"
            ]

            for selector in score_selectors:
                score_element = soup.select_one(selector)
                if score_element:
                    mythic_plus_data["rio_score"] = score_element.get_text(strip=True)
                    break

            # Look for any text that mentions mythic+ or score
            page_text = soup.get_text()
            if "mythic" in page_text.lower():
                mythic_plus_data["page_contains_mythic"] = True
            if "score" in page_text.lower():
                mythic_plus_data["page_contains_score"] = True

            # Try to find dungeon runs
            run_selectors = [
                ".dungeon-run",
                ".run",
                "[data-testid='dungeon-run']"
            ]

            best_runs = []
            for selector in run_selectors:
                run_elements = soup.select(selector)
                if run_elements:
                    for run in run_elements[:5]:  # Get top 5 runs
                        run_text = run.get_text(strip=True)
                        if run_text:
                            best_runs.append({"run_text": run_text})
                    break

            mythic_plus_data["best_runs"] = best_runs

        except Exception as e:
            print(f"Error extracting M+ data: {e}")

        return mythic_plus_data
    
    def extract_raid_progress(self, soup: BeautifulSoup) -> Dict:
        """Extract raid progression data"""
        raid_data = {}
        
        try:
            # Current tier progress
            raid_progress_elements = soup.find_all("div", class_="raid-progress")
            for progress in raid_progress_elements:
                raid_name_element = progress.find("div", class_="raid-name")
                progress_element = progress.find("div", class_="progress-text")
                
                if raid_name_element and progress_element:
                    raid_name = raid_name_element.get_text(strip=True)
                    progress_text = progress_element.get_text(strip=True)
                    raid_data[raid_name] = progress_text
                    
        except Exception as e:
            print(f"Error extracting raid data: {e}")
        
        return raid_data
    
    def scrape_character(self, character_name: str, region: str = None, server: str = None) -> Dict:
        """Scrape data for a single character"""
        print(f"Scraping character: {character_name}")
        
        url = self.build_character_url(character_name, region, server)
        print(f"URL: {url}")
        
        character_data = {
            "character_name": character_name,
            "url": url,
            "scraped_at": time.strftime("%Y-%m-%d %H:%M:%S"),
            "success": False
        }
        
        try:
            self.driver.get(url)
            
            if not self.wait_for_page_load():
                character_data["error"] = "Page load timeout"
                return character_data
            
            # Get page source and parse with BeautifulSoup
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')

            # Save a sample of the HTML for debugging (first character only)
            if character_name == "Célesti":  # Save only for the first character to avoid clutter
                debug_filename = f"debug_page_{character_name.replace('é', 'e')}.html"
                with open(debug_filename, 'w', encoding='utf-8') as f:
                    f.write(page_source)
                print(f"Saved debug HTML to {debug_filename}")
            
            # Extract different types of data based on config
            if config.EXTRACT_DATA.get("basic_info", True):
                character_data["basic_info"] = self.extract_basic_info(soup)
            
            if config.EXTRACT_DATA.get("mythic_plus", True):
                character_data["mythic_plus"] = self.extract_mythic_plus_data(soup)
            
            if config.EXTRACT_DATA.get("raid_progress", True):
                character_data["raid_progress"] = self.extract_raid_progress(soup)
            
            character_data["success"] = True
            print(f"Successfully scraped {character_name}")
            
        except Exception as e:
            print(f"Error scraping {character_name}: {e}")
            character_data["error"] = str(e)
        
        return character_data
    
    def load_characters_from_file(self, filename: str = None) -> List[str]:
        """Load character names from file"""
        filename = filename or config.CHARACTER_FILE
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                characters = [line.strip() for line in f if line.strip()]
            print(f"Loaded {len(characters)} characters from {filename}")
            return characters
        except FileNotFoundError:
            print(f"Character file {filename} not found!")
            return []
        except Exception as e:
            print(f"Error reading character file: {e}")
            return []
    
    def save_data(self, data: List[Dict], format_type: str = None):
        """Save scraped data to file"""
        format_type = format_type or config.OUTPUT_FORMAT
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        
        if format_type in ["json", "both"]:
            filename = f"{config.OUTPUT_FILENAME}_{timestamp}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"Data saved to {filename}")
        
        if format_type in ["csv", "both"]:
            filename = f"{config.OUTPUT_FILENAME}_{timestamp}.csv"
            # Flatten the nested data for CSV
            flattened_data = []
            for item in data:
                flat_item = {"character_name": item.get("character_name", "")}
                flat_item.update(item.get("basic_info", {}))
                flat_item.update(item.get("mythic_plus", {}))
                # Add other fields as needed
                flattened_data.append(flat_item)
            
            df = pd.DataFrame(flattened_data)
            df.to_csv(filename, index=False, encoding='utf-8')
            print(f"Data saved to {filename}")
    
    def run_scraper(self):
        """Main method to run the scraper"""
        print("Starting Raider.IO Character Scraper")
        print("=" * 50)
        
        # Load characters
        characters = self.load_characters_from_file()
        if not characters:
            print("No characters to scrape. Exiting.")
            return
        
        # Setup browser
        self.setup_driver()
        
        try:
            # Scrape each character
            for i, character in enumerate(characters, 1):
                print(f"\n[{i}/{len(characters)}] Processing: {character}")
                
                character_data = self.scrape_character(character)
                self.scraped_data.append(character_data)
                
                # Rate limiting
                if i < len(characters):  # Don't wait after the last character
                    print(f"Waiting {config.REQUEST_DELAY} seconds...")
                    time.sleep(config.REQUEST_DELAY)
            
            # Save results
            print(f"\nScraping complete! Processed {len(self.scraped_data)} characters.")
            self.save_data(self.scraped_data)
            
            # Print summary
            successful = sum(1 for item in self.scraped_data if item.get("success", False))
            print(f"Successful: {successful}/{len(self.scraped_data)}")
            
        finally:
            self.close_driver()


def main():
    """Main entry point"""
    scraper = RaiderIOScraper()
    scraper.run_scraper()


if __name__ == "__main__":
    main()
