[{"character_name": "<PERSON><PERSON><PERSON>", "url": "https://raider.io/characters/eu/drakthul/Yonteaux", "scraped_at": "2025-07-30 14:58:07", "success": true, "mythic_plus": {"rio_score": null, "total_runs": 10, "runs_by_date": [{"dungeon": "", "keystone_level": 0, "completed_at": "", "clear_time_ms": 0, "keystone_time_ms": 0, "num_chests": 0, "score": 332.463, "affixes": [], "url": "", "status": "", "season": ""}, {"dungeon": "", "keystone_level": 0, "completed_at": "", "clear_time_ms": 0, "keystone_time_ms": 0, "num_chests": 0, "score": 335, "affixes": [], "url": "", "status": "", "season": ""}, {"dungeon": "", "keystone_level": 0, "completed_at": "", "clear_time_ms": 0, "keystone_time_ms": 0, "num_chests": 0, "score": 441.279, "affixes": [], "url": "", "status": "", "season": ""}, {"dungeon": "", "keystone_level": 0, "completed_at": "", "clear_time_ms": 0, "keystone_time_ms": 0, "num_chests": 0, "score": 428.992, "affixes": [], "url": "", "status": "", "season": ""}, {"dungeon": "", "keystone_level": 0, "completed_at": "", "clear_time_ms": 0, "keystone_time_ms": 0, "num_chests": 0, "score": 425.991, "affixes": [], "url": "", "status": "", "season": ""}, {"dungeon": "", "keystone_level": 0, "completed_at": "", "clear_time_ms": 0, "keystone_time_ms": 0, "num_chests": 0, "score": 412.798, "affixes": [], "url": "", "status": "", "season": ""}, {"dungeon": "", "keystone_level": 0, "completed_at": "", "clear_time_ms": 0, "keystone_time_ms": 0, "num_chests": 0, "score": 317.456, "affixes": [], "url": "", "status": "", "season": ""}, {"dungeon": "", "keystone_level": 0, "completed_at": "", "clear_time_ms": 0, "keystone_time_ms": 0, "num_chests": 0, "score": 417.062, "affixes": [], "url": "", "status": "", "season": ""}, {"dungeon": "", "keystone_level": 0, "completed_at": "", "clear_time_ms": 0, "keystone_time_ms": 0, "num_chests": 0, "score": 413.102, "affixes": [], "url": "", "status": "", "season": ""}, {"dungeon": "", "keystone_level": 0, "completed_at": "", "clear_time_ms": 0, "keystone_time_ms": 0, "num_chests": 0, "score": 411.22, "affixes": [], "url": "", "status": "", "season": ""}], "best_runs": [{"dungeon": "", "keystone_level": 0, "completed_at": "", "clear_time_ms": 0, "keystone_time_ms": 0, "num_chests": 0, "score": 441.279, "affixes": [], "url": "", "status": "", "season": ""}, {"dungeon": "", "keystone_level": 0, "completed_at": "", "clear_time_ms": 0, "keystone_time_ms": 0, "num_chests": 0, "score": 428.992, "affixes": [], "url": "", "status": "", "season": ""}, {"dungeon": "", "keystone_level": 0, "completed_at": "", "clear_time_ms": 0, "keystone_time_ms": 0, "num_chests": 0, "score": 425.991, "affixes": [], "url": "", "status": "", "season": ""}, {"dungeon": "", "keystone_level": 0, "completed_at": "", "clear_time_ms": 0, "keystone_time_ms": 0, "num_chests": 0, "score": 417.062, "affixes": [], "url": "", "status": "", "season": ""}, {"dungeon": "", "keystone_level": 0, "completed_at": "", "clear_time_ms": 0, "keystone_time_ms": 0, "num_chests": 0, "score": 413.102, "affixes": [], "url": "", "status": "", "season": ""}, {"dungeon": "", "keystone_level": 0, "completed_at": "", "clear_time_ms": 0, "keystone_time_ms": 0, "num_chests": 0, "score": 412.798, "affixes": [], "url": "", "status": "", "season": ""}, {"dungeon": "", "keystone_level": 0, "completed_at": "", "clear_time_ms": 0, "keystone_time_ms": 0, "num_chests": 0, "score": 411.22, "affixes": [], "url": "", "status": "", "season": ""}, {"dungeon": "", "keystone_level": 0, "completed_at": "", "clear_time_ms": 0, "keystone_time_ms": 0, "num_chests": 0, "score": 335, "affixes": [], "url": "", "status": "", "season": ""}, {"dungeon": "", "keystone_level": 0, "completed_at": "", "clear_time_ms": 0, "keystone_time_ms": 0, "num_chests": 0, "score": 332.463, "affixes": [], "url": "", "status": "", "season": ""}, {"dungeon": "", "keystone_level": 0, "completed_at": "", "clear_time_ms": 0, "keystone_time_ms": 0, "num_chests": 0, "score": 317.456, "affixes": [], "url": "", "status": "", "season": ""}], "season_summary": {"season_id": "season-tww-2", "total_score": 0, "dungeons_completed": 0, "highest_level_completed": 0}, "character_rankings": {}, "character_name": "<PERSON><PERSON><PERSON>", "character_class": "<PERSON><PERSON><PERSON>", "character_spec": "", "character_level": 80}}]