#!/usr/bin/env python3
"""
Enhanced Raider.IO Character Scraper with detailed M+ data extraction

This script extracts:
- M+ Score (Rio Score)
- Total number of keystone runs
- Dates of all runs
- Detailed run information
"""

import json
import csv
import time
import urllib.parse
import re
from typing import Dict, List, Optional
from pathlib import Path
from datetime import datetime

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
import chromedriver_binary
from bs4 import BeautifulSoup
import pandas as pd

import config


class EnhancedRaiderIOScraper:
    """Enhanced scraper class for detailed raider.io character data"""
    
    def __init__(self):
        self.driver = None
        self.scraped_data = []
        
    def setup_driver(self):
        """Initialize the Selenium WebDriver"""
        print("Setting up browser driver...")
        
        chrome_options = Options()
        if config.HEADLESS_BROWSER:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        chromedriver_binary.add_chromedriver_to_path()
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.set_page_load_timeout(config.PAGE_LOAD_TIMEOUT)
        
        print("Browser driver setup complete.")
    
    def close_driver(self):
        """Close the WebDriver"""
        if self.driver:
            self.driver.quit()
            print("Browser driver closed.")
    
    def encode_character_name(self, name: str) -> str:
        """Properly encode character name for URL"""
        return urllib.parse.quote(name, safe='')
    
    def build_character_url(self, character_name: str, region: str = None, server: str = None) -> str:
        """Build the full URL for a character"""
        region = region or config.DEFAULT_REGION
        server = server or config.DEFAULT_SERVER
        encoded_name = self.encode_character_name(character_name)
        
        url = f"{config.BASE_URL}/characters/{region}/{server}/{encoded_name}"
        return url
    
    def wait_for_page_load(self):
        """Wait for the page to fully load"""
        try:
            WebDriverWait(self.driver, config.SELENIUM_TIMEOUT).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )
            
            # Give extra time for JavaScript to load content
            time.sleep(5)
            
            page_source = self.driver.page_source
            if len(page_source) > 50000:
                return True
            else:
                print("Page content seems too small, may not be fully loaded")
                return False
                
        except Exception:
            print("Timeout waiting for page to load")
            return False
    
    def get_dungeon_name_by_zone_id(self, zone_id: int) -> str:
        """Get dungeon name by zone ID"""
        # Common TWW Season 2 dungeon zone IDs
        dungeon_map = {
            8064: "Atal'Dazar",
            12841: "Theater of Pain",
            14882: "Siege of Boralus",
            14938: "The Dawnbreaker",
            14954: "City of Threads",
            15103: "Ara-Kara, City of Echoes",
            15452: "The Stonevault",
            800002: "Grim Batol"
        }
        return dungeon_map.get(zone_id, f"Unknown Dungeon (ID: {zone_id})")

    def extract_rio_initial_data(self, page_source: str) -> Dict:
        """Extract the __RIO_INITIAL_DATA from page source"""
        try:
            pattern = r'window\.__RIO_INITIAL_DATA\s*=\s*"(.*?)";'
            match = re.search(pattern, page_source, re.DOTALL)
            
            if not match:
                pattern = r'__RIO_INITIAL_DATA\s*=\s*"(.*?)"'
                match = re.search(pattern, page_source, re.DOTALL)
            
            if match:
                json_string = match.group(1)
                
                # Try different unescaping approaches
                try:
                    data = json.loads(json_string)
                    return data
                except json.JSONDecodeError:
                    try:
                        unescaped = json_string.replace('\\"', '"').replace('\\\\', '\\')
                        data = json.loads(unescaped)
                        return data
                    except json.JSONDecodeError:
                        try:
                            import codecs
                            decoded = codecs.decode(json_string, 'unicode_escape')
                            data = json.loads(decoded)
                            return data
                        except:
                            print("Failed to parse RIO initial data")
                            return {}
            else:
                print("Could not find RIO initial data in page")
                return {}
                
        except Exception as e:
            print(f"Error extracting RIO data: {e}")
            return {}
    
    def extract_mythic_plus_data(self, rio_data: Dict) -> Dict:
        """Extract detailed Mythic+ data from RIO initial data"""
        mythic_plus_data = {
            "rio_score": None,
            "total_runs": 0,
            "runs_by_date": [],
            "best_runs": [],
            "season_summary": {},
            "character_rankings": {},
            "character_name": "",
            "character_class": "",
            "character_spec": "",
            "character_level": 0
        }

        try:
            # Extract character details for basic info
            char_details = rio_data.get('characterDetails', {})
            if char_details and 'character' in char_details:
                character_info = char_details['character']
                mythic_plus_data["character_name"] = character_info.get('name', '')
                mythic_plus_data["character_class"] = character_info.get('class', {}).get('name', '')
                mythic_plus_data["character_spec"] = character_info.get('activeSpec', {}).get('name', '')
                mythic_plus_data["character_level"] = character_info.get('level', 0)

                # Look for M+ score in character info
                if 'mythicPlusScores' in character_info:
                    scores = character_info['mythicPlusScores']
                    if scores and isinstance(scores, dict):
                        # Get current season score
                        current_season = scores.get('all', {})
                        if current_season:
                            mythic_plus_data["rio_score"] = current_season.get('score', 0)
            
            # Extract M+ runs data from keystoneRuns (this contains more runs after expansion)
            keystone_runs = rio_data.get('keystoneRuns', {})
            all_keystone_runs = []

            if keystone_runs and 'dungeonRuns' in keystone_runs:
                dungeon_runs = keystone_runs['dungeonRuns']

                # Process all run categories to get the REAL total run count
                # Look for both STANDARD and RECENT categories
                for category_key, runs_list in dungeon_runs.items():
                    if isinstance(runs_list, list):
                        print(f"Found {len(runs_list)} runs in category: {category_key}")

                        for run in runs_list:
                            # The actual run data is in the 'summary' field
                            summary = run.get('summary', {})
                            if summary:
                                run_info = {
                                    "dungeon": summary.get('dungeon', {}).get('name', ''),
                                    "keystone_level": summary.get('mythic_level', 0),  # Fixed field name
                                    "completed_at": summary.get('completed_at', ''),  # Fixed field name
                                    "clear_time_ms": summary.get('clear_time_ms', 0),  # Fixed field name
                                    "keystone_time_ms": summary.get('keystone_time_ms', 0),  # Fixed field name
                                    "num_chests": summary.get('num_chests', 0),  # Fixed field name
                                    "score": run.get('score', 0),  # Score is in parent run object, not summary
                                    "affixes": [mod.get('name', '') for mod in summary.get('weekly_modifiers', [])],  # Fixed field name
                                    "url": summary.get('url', ''),
                                    "status": summary.get('status', ''),
                                    "season": summary.get('season', ''),
                                    "dungeon_short_name": summary.get('dungeon', {}).get('short_name', ''),
                                    "weekly_modifiers": [mod.get('name', '') for mod in summary.get('weekly_modifiers', [])],
                                    "category": category_key,  # Track which category this run came from
                                    "run_id": summary.get('keystone_run_id', ''),  # Fixed field name
                                    "logged_run_id": summary.get('logged_run_id', ''),
                                    "time_remaining_ms": summary.get('time_remaining_ms', 0),
                                }
                            else:
                                # Fallback to direct run data if no summary
                                run_info = {
                                    "dungeon": run.get('dungeon', {}).get('name', ''),
                                    "keystone_level": run.get('mythic_level', 0),  # Fixed field name
                                    "completed_at": run.get('completed_at', ''),  # Fixed field name
                                    "clear_time_ms": run.get('clear_time_ms', 0),  # Fixed field name
                                    "keystone_time_ms": run.get('keystone_time_ms', 0),  # Fixed field name
                                    "num_chests": run.get('num_chests', 0),  # Fixed field name
                                    "score": run.get('score', 0),
                                    "affixes": [affix.get('name', '') for affix in run.get('weekly_modifiers', [])],  # Fixed field name
                                    "url": run.get('url', ''),
                                    "status": run.get('status', ''),
                                    "season": run.get('season', ''),
                                    "dungeon_short_name": run.get('dungeon', {}).get('short_name', ''),
                                    "weekly_modifiers": [],
                                    "category": category_key,
                                    "run_id": run.get('keystone_run_id', ''),  # Fixed field name
                                }

                            # Convert timestamp to readable date
                            if run_info["completed_at"]:
                                try:
                                    # Handle different timestamp formats
                                    if isinstance(run_info["completed_at"], str):
                                        # ISO format
                                        from datetime import datetime
                                        date_obj = datetime.fromisoformat(run_info["completed_at"].replace('Z', '+00:00'))
                                    else:
                                        # Unix timestamp
                                        timestamp = int(run_info["completed_at"]) / 1000
                                        date_obj = datetime.fromtimestamp(timestamp)

                                    run_info["completed_date"] = date_obj.strftime("%Y-%m-%d")
                                    run_info["completed_datetime"] = date_obj.strftime("%Y-%m-%d %H:%M:%S")
                                except Exception as e:
                                    print(f"Error parsing date {run_info['completed_at']}: {e}")
                                    run_info["completed_date"] = "Unknown"
                                    run_info["completed_datetime"] = "Unknown"

                            all_keystone_runs.append(run_info)

            # Also check characterMythicPlusRuns for additional data
            char_mp_runs = rio_data.get('characterMythicPlusRuns', {})
            if char_mp_runs and 'dungeons' in char_mp_runs:
                dungeons_data = char_mp_runs['dungeons']
                for dungeon_info in dungeons_data:
                    if 'runs' in dungeon_info:
                        for run in dungeon_info['runs']:
                            # Add runs that might not be in keystoneRuns
                            run_info = {
                                "dungeon": run.get('dungeon', {}).get('name', ''),
                                "keystone_level": run.get('mythicLevel', 0),
                                "completed_at": run.get('completedAt', ''),
                                "clear_time_ms": run.get('clearTimeMs', 0),
                                "keystone_time_ms": run.get('keystoneTimeMs', 0),
                                "num_chests": run.get('numChests', 0),
                                "score": run.get('score', 0),
                                "affixes": [affix.get('name', '') for affix in run.get('affixes', [])],
                                "url": run.get('url', ''),
                                "status": run.get('status', ''),
                                "season": run.get('season', '')
                            }

                            # Convert timestamp to readable date
                            if run_info["completed_at"]:
                                try:
                                    if isinstance(run_info["completed_at"], str):
                                        date_obj = datetime.fromisoformat(run_info["completed_at"].replace('Z', '+00:00'))
                                    else:
                                        timestamp = int(run_info["completed_at"]) / 1000
                                        date_obj = datetime.fromtimestamp(timestamp)

                                    run_info["completed_date"] = date_obj.strftime("%Y-%m-%d")
                                    run_info["completed_datetime"] = date_obj.strftime("%Y-%m-%d %H:%M:%S")
                                except:
                                    run_info["completed_date"] = "Unknown"
                                    run_info["completed_datetime"] = "Unknown"

                            all_keystone_runs.append(run_info)

            # Remove duplicates from keystone runs and set total runs
            unique_keystone_runs = []
            seen_runs = set()

            for run in all_keystone_runs:
                # Create a unique identifier for each run using multiple fields
                # Use run_id if available, otherwise create from other fields
                if run.get('run_id'):
                    run_identifier = f"run_{run['run_id']}"
                else:
                    run_identifier = f"{run['dungeon']}_{run['keystone_level']}_{run['completed_at']}_{run['score']}_{run.get('category', '')}"

                if run_identifier not in seen_runs:
                    seen_runs.add(run_identifier)
                    unique_keystone_runs.append(run)

            print(f"Total unique keystone runs found: {len(unique_keystone_runs)}")

            # Set the REAL total run count
            mythic_plus_data["total_runs"] = len(unique_keystone_runs)

            # Sort runs by score (highest first) since we don't have reliable dates
            unique_keystone_runs.sort(key=lambda x: x.get("score", 0), reverse=True)

            # If we have keystone runs, use them; otherwise fall back to score runs
            if unique_keystone_runs:
                mythic_plus_data["runs_by_date"] = unique_keystone_runs
                mythic_plus_data["best_runs"] = unique_keystone_runs[:10]
            else:
                print("No keystone runs found, will use score runs as fallback")
            
            # Extract M+ progress data and score
            char_mp_progress = rio_data.get('characterMythicPlusProgress', {})
            if char_mp_progress and 'mythicPlusProgress' in char_mp_progress:
                progress_data = char_mp_progress['mythicPlusProgress']

                # Get current season data
                current_season_key = None
                for key in progress_data.keys():
                    if 'season-tww' in key:  # Current expansion seasons
                        current_season_key = key
                        break

                if current_season_key and progress_data[current_season_key]:
                    season_data = progress_data[current_season_key]

                    # Extract M+ scores
                    if 'mythicPlusScores' in season_data:
                        mp_scores = season_data['mythicPlusScores']

                        # Get overall score
                        if 'all' in mp_scores and mp_scores['all'].get('score'):
                            mythic_plus_data["rio_score"] = mp_scores['all']['score']

                        # Extract detailed runs from the scores data (FALLBACK if no keystone runs)
                        if 'all' in mp_scores and 'runs' in mp_scores['all'] and not unique_keystone_runs:
                            print("Using score runs as fallback (these are only best runs per dungeon)")
                            score_runs = mp_scores['all']['runs']

                            score_run_list = []

                            for run in score_runs:
                                run_info = {
                                    "dungeon": self.get_dungeon_name_by_zone_id(run.get('zoneId', 0)),
                                    "keystone_level": run.get('mythicLevel', 0),
                                    "completed_at": "",  # This data doesn't have timestamps
                                    "clear_time_ms": run.get('clearTimeMs', 0),
                                    "keystone_time_ms": 0,  # Not available in this data
                                    "num_chests": 0,  # Not available in this data
                                    "score": run.get('score', 0),
                                    "affixes": [str(affix) for affix in run.get('affixes', [])],
                                    "url": "",
                                    "status": "completed",
                                    "season": current_season_key,
                                    "dungeon_short_name": "",
                                    "weekly_modifiers": [],
                                    "keystone_run_id": run.get('keystoneRunId', 0),
                                    "period": run.get('period', 0),
                                    "zone_id": run.get('zoneId', 0),
                                    "category": "best_score_runs"
                                }

                                # Convert clear time to readable format
                                if run_info["clear_time_ms"]:
                                    minutes = run_info["clear_time_ms"] // 60000
                                    seconds = (run_info["clear_time_ms"] % 60000) // 1000
                                    run_info["clear_time_formatted"] = f"{minutes}:{seconds:02d}"

                                score_run_list.append(run_info)

                            # Only use score runs if we don't have keystone runs
                            if not mythic_plus_data.get("runs_by_date"):
                                mythic_plus_data["runs_by_date"] = score_run_list
                                mythic_plus_data["total_runs"] = len(score_run_list)
                                mythic_plus_data["best_runs"] = score_run_list[:10]
                                print(f"Using {len(score_run_list)} score runs as fallback (NOTE: This is only best runs per dungeon, not total runs)")

                        elif 'all' in mp_scores and 'runs' in mp_scores['all']:
                            # We have keystone runs, but also store the best score runs for reference
                            score_runs = mp_scores['all']['runs']
                            mythic_plus_data["best_score_runs"] = []

                            for run in score_runs:
                                run_info = {
                                    "dungeon": self.get_dungeon_name_by_zone_id(run.get('zoneId', 0)),
                                    "keystone_level": run.get('mythicLevel', 0),
                                    "score": run.get('score', 0),
                                    "clear_time_ms": run.get('clearTimeMs', 0),
                                }

                                if run_info["clear_time_ms"]:
                                    minutes = run_info["clear_time_ms"] // 60000
                                    seconds = (run_info["clear_time_ms"] % 60000) // 1000
                                    run_info["clear_time_formatted"] = f"{minutes}:{seconds:02d}"

                                mythic_plus_data["best_score_runs"].append(run_info)

                    # Season summary
                    mythic_plus_data["season_summary"] = {
                        "season_id": current_season_key,
                        "total_score": mythic_plus_data.get("rio_score", 0),
                        "dungeons_completed": len(mythic_plus_data.get("runs_by_date", [])),
                        "highest_level_completed": max([run.get('keystone_level', 0) for run in mythic_plus_data.get("runs_by_date", [])], default=0)
                    }

            # Extract character rankings - this might be empty for some characters
            char_rankings = rio_data.get('keystoneCharacterRankings', {})
            if char_rankings and 'instances' in char_rankings:
                instances = char_rankings['instances']
                if instances:
                    # Get the main ranking instance
                    main_rankings = instances.get('main', {})
                    if main_rankings:
                        mythic_plus_data["character_rankings"] = {
                            "world_rank": main_rankings.get('world', {}).get('rank', 0),
                            "region_rank": main_rankings.get('region', {}).get('rank', 0),
                            "realm_rank": main_rankings.get('realm', {}).get('rank', 0),
                            "class_rank": main_rankings.get('class', {}).get('rank', 0),
                            "spec_rank": main_rankings.get('spec', {}).get('rank', 0)
                        }
            
        except Exception as e:
            print(f"Error extracting M+ data: {e}")
        
        return mythic_plus_data
    
    def scrape_character(self, character_name: str, region: str = None, server: str = None) -> Dict:
        """Scrape detailed data for a single character"""
        print(f"Scraping character: {character_name}")
        
        url = self.build_character_url(character_name, region, server)
        print(f"URL: {url}")
        
        character_data = {
            "character_name": character_name,
            "url": url,
            "scraped_at": time.strftime("%Y-%m-%d %H:%M:%S"),
            "success": False
        }
        
        try:
            self.driver.get(url)
            
            if not self.wait_for_page_load():
                character_data["error"] = "Page load timeout"
                return character_data
            
            # Get page source and extract RIO data
            page_source = self.driver.page_source
            rio_data = self.extract_rio_initial_data(page_source)
            
            if not rio_data:
                character_data["error"] = "Could not extract RIO data"
                return character_data
            
            # Extract detailed M+ data
            mythic_plus_data = self.extract_mythic_plus_data(rio_data)
            character_data["mythic_plus"] = mythic_plus_data
            
            character_data["success"] = True
            print(f"Successfully scraped {character_name}")
            print(f"  Rio Score: {mythic_plus_data.get('rio_score', 'N/A')}")
            print(f"  Total Runs: {mythic_plus_data.get('total_runs', 0)}")
            
        except Exception as e:
            print(f"Error scraping {character_name}: {e}")
            character_data["error"] = str(e)
        
        return character_data
    
    def load_characters_from_file(self, filename: str = None) -> List[str]:
        """Load character names from file"""
        filename = filename or config.CHARACTER_FILE
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                characters = [line.strip() for line in f if line.strip()]
            print(f"Loaded {len(characters)} characters from {filename}")
            return characters
        except FileNotFoundError:
            print(f"Character file {filename} not found!")
            return []
        except Exception as e:
            print(f"Error reading character file: {e}")
            return []
    
    def save_data(self, data: List[Dict], format_type: str = None):
        """Save scraped data to file"""
        format_type = format_type or config.OUTPUT_FORMAT
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        
        if format_type in ["json", "both"]:
            filename = f"enhanced_characters_{timestamp}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"Data saved to {filename}")
        
        if format_type in ["csv", "both"]:
            filename = f"enhanced_characters_{timestamp}.csv"
            # Create flattened data for CSV
            flattened_data = []
            for item in data:
                if item.get("success") and "mythic_plus" in item:
                    mp_data = item["mythic_plus"]
                    flat_item = {
                        "character_name": item.get("character_name", ""),
                        "rio_score": mp_data.get("rio_score", 0),
                        "total_runs": mp_data.get("total_runs", 0),
                        "character_class": mp_data.get("character_class", ""),
                        "character_spec": mp_data.get("character_spec", ""),
                        "character_level": mp_data.get("character_level", 0),
                        "world_rank": mp_data.get("character_rankings", {}).get("world_rank", 0),
                        "region_rank": mp_data.get("character_rankings", {}).get("region_rank", 0),
                        "realm_rank": mp_data.get("character_rankings", {}).get("realm_rank", 0),
                        "highest_key_level": mp_data.get("season_summary", {}).get("highest_level_completed", 0)
                    }
                    flattened_data.append(flat_item)
            
            if flattened_data:
                df = pd.DataFrame(flattened_data)
                df.to_csv(filename, index=False, encoding='utf-8')
                print(f"Data saved to {filename}")
        
        # Also save detailed runs data
        if format_type in ["csv", "both"]:
            runs_filename = f"character_runs_{timestamp}.csv"
            all_runs = []
            for item in data:
                if item.get("success") and "mythic_plus" in item:
                    char_name = item.get("character_name", "")
                    runs = item["mythic_plus"].get("runs_by_date", [])
                    for run in runs:
                        run_record = {
                            "character_name": char_name,
                            **run
                        }
                        all_runs.append(run_record)
            
            if all_runs:
                runs_df = pd.DataFrame(all_runs)
                runs_df.to_csv(runs_filename, index=False, encoding='utf-8')
                print(f"Detailed runs data saved to {runs_filename}")
    
    def run_scraper(self):
        """Main method to run the enhanced scraper"""
        print("Starting Enhanced Raider.IO Character Scraper")
        print("=" * 60)
        
        characters = self.load_characters_from_file()
        if not characters:
            print("No characters to scrape. Exiting.")
            return
        
        self.setup_driver()
        
        try:
            for i, character in enumerate(characters, 1):
                print(f"\n[{i}/{len(characters)}] Processing: {character}")
                
                character_data = self.scrape_character(character)
                self.scraped_data.append(character_data)
                
                if i < len(characters):
                    print(f"Waiting {config.REQUEST_DELAY} seconds...")
                    time.sleep(config.REQUEST_DELAY)
            
            print(f"\nScraping complete! Processed {len(self.scraped_data)} characters.")
            self.save_data(self.scraped_data)
            
            # Print summary
            successful = sum(1 for item in self.scraped_data if item.get("success", False))
            print(f"Successful: {successful}/{len(self.scraped_data)}")
            
            # Print detailed summary for successful characters
            for item in self.scraped_data:
                if item.get("success") and "mythic_plus" in item:
                    mp_data = item["mythic_plus"]
                    print(f"\n{item['character_name']}:")
                    print(f"  Rio Score: {mp_data.get('rio_score', 'N/A')}")
                    print(f"  Total Runs: {mp_data.get('total_runs', 0)}")
                    print(f"  Class/Spec: {mp_data.get('character_class', '')} {mp_data.get('character_spec', '')}")
                    
                    rankings = mp_data.get('character_rankings', {})
                    if rankings.get('world_rank'):
                        print(f"  World Rank: {rankings['world_rank']}")
                        print(f"  Region Rank: {rankings['region_rank']}")
                        print(f"  Realm Rank: {rankings['realm_rank']}")
            
        finally:
            self.close_driver()


def main():
    """Main entry point"""
    scraper = EnhancedRaiderIOScraper()
    scraper.run_scraper()


if __name__ == "__main__":
    main()
