#!/usr/bin/env python3
"""
Investigate how to get the complete run history for a character
"""

import time
import json
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
import chromedriver_binary

def investigate_full_runs(character_name="seedhy", region="eu", server="stormscale"):
    """Investigate how to get complete run history"""
    
    print(f"Investigating full run history for: {character_name}")
    
    chrome_options = Options()
    # Don't use headless so we can see what's happening
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    
    chromedriver_binary.add_chromedriver_to_path()
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        url = f"https://raider.io/characters/{region}/{server}/{character_name}"
        print(f"Loading: {url}")
        
        driver.get(url)
        
        WebDriverWait(driver, 10).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
        time.sleep(5)
        
        print("Page loaded. Looking for M+ section...")
        
        # Look for M+ related elements on the page
        try:
            # Look for M+ section or tabs
            mythic_plus_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'Mythic+') or contains(text(), 'M+') or contains(text(), 'mythic')]")
            print(f"Found {len(mythic_plus_elements)} M+ related elements")
            
            for i, elem in enumerate(mythic_plus_elements[:5]):
                try:
                    print(f"  {i+1}. {elem.tag_name}: {elem.text[:100]}")
                except:
                    print(f"  {i+1}. {elem.tag_name}: [Could not get text]")
            
            # Look for "Show more" or "Load more" buttons
            load_more_buttons = driver.find_elements(By.XPATH, "//*[contains(text(), 'Show more') or contains(text(), 'Load more') or contains(text(), 'View all') or contains(text(), 'See all')]")
            print(f"\nFound {len(load_more_buttons)} potential load more buttons:")
            
            for i, button in enumerate(load_more_buttons):
                try:
                    print(f"  {i+1}. {button.tag_name}: {button.text}")
                    print(f"      Classes: {button.get_attribute('class')}")
                except:
                    print(f"  {i+1}. [Could not get button info]")
            
            # Look for pagination or run count indicators
            run_count_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'run') or contains(text(), 'Run')]")
            print(f"\nFound {len(run_count_elements)} run-related elements:")
            
            for i, elem in enumerate(run_count_elements[:10]):
                try:
                    text = elem.text.strip()
                    if text and ('run' in text.lower() or any(char.isdigit() for char in text)):
                        print(f"  {i+1}. {elem.tag_name}: {text}")
                except:
                    pass
            
            # Try to click on M+ section if it exists
            try:
                mythic_plus_tab = driver.find_element(By.XPATH, "//a[contains(@href, 'mythic-plus') or contains(text(), 'Mythic+')]")
                print(f"\nFound M+ tab/link: {mythic_plus_tab.get_attribute('href')}")
                
                # Click on it
                driver.execute_script("arguments[0].click();", mythic_plus_tab)
                time.sleep(3)
                print("Clicked on M+ section")
                
                # Check if more data loaded
                page_source_after = driver.page_source
                if 'keystoneRuns' in page_source_after:
                    print("Page still contains keystoneRuns data")
                
            except Exception as e:
                print(f"Could not find or click M+ tab: {e}")
            
            # Look for any AJAX/API calls in network tab (we can't directly access this, but we can check for loading indicators)
            loading_elements = driver.find_elements(By.XPATH, "//*[contains(@class, 'loading') or contains(@class, 'spinner')]")
            print(f"\nFound {len(loading_elements)} loading indicators")
            
            # Check if there are any iframes that might contain the run data
            iframes = driver.find_elements(By.TAG_NAME, "iframe")
            print(f"\nFound {len(iframes)} iframes")
            
            # Look for any data attributes that might indicate more data
            elements_with_data = driver.find_elements(By.XPATH, "//*[@data-*]")
            print(f"\nFound {len(elements_with_data)} elements with data attributes")
            
            # Check for any React components that might handle run loading
            react_elements = driver.find_elements(By.XPATH, "//*[contains(@class, 'react') or contains(@data-react, '')]")
            print(f"Found {len(react_elements)} potential React elements")
            
        except Exception as e:
            print(f"Error investigating page elements: {e}")
        
        # Extract current RIO data to see what we have
        page_source = driver.page_source
        pattern = r'window\.__RIO_INITIAL_DATA\s*=\s*"(.*?)";'
        match = re.search(pattern, page_source, re.DOTALL)
        
        if match:
            json_string = match.group(1)
            
            try:
                data = json.loads(json_string)
            except json.JSONDecodeError:
                try:
                    unescaped = json_string.replace('\\"', '"').replace('\\\\', '\\')
                    data = json.loads(unescaped)
                except json.JSONDecodeError:
                    import codecs
                    decoded = codecs.decode(json_string, 'unicode_escape')
                    data = json.loads(decoded)
            
            print(f"\n" + "="*60)
            print("ANALYZING CURRENT RIO DATA FOR CLUES")
            print("="*60)
            
            # Look for any pagination or loading indicators in the data
            keystone_runs = data.get('keystoneRuns', {})
            if keystone_runs:
                print(f"keystoneRuns keys: {list(keystone_runs.keys())}")
                
                # Check for pagination info
                if 'pagination' in keystone_runs:
                    print(f"Pagination info: {keystone_runs['pagination']}")
                
                if 'loadingDungeons' in keystone_runs:
                    print(f"Loading dungeons: {keystone_runs['loadingDungeons']}")
                
                if 'loadedDungeons' in keystone_runs:
                    print(f"Loaded dungeons: {keystone_runs['loadedDungeons']}")
                
                # Check for any URLs or endpoints
                for key, value in keystone_runs.items():
                    if isinstance(value, str) and ('http' in value or 'api' in value):
                        print(f"Found URL in {key}: {value}")
            
            # Look for any API endpoints or URLs in the entire data
            def find_urls(obj, path=""):
                urls = []
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        current_path = f"{path}.{key}" if path else key
                        if isinstance(value, str) and ('http' in value or 'api' in value or '/characters/' in value):
                            urls.append(f"{current_path}: {value}")
                        elif isinstance(value, (dict, list)):
                            urls.extend(find_urls(value, current_path))
                elif isinstance(obj, list):
                    for i, item in enumerate(obj):
                        urls.extend(find_urls(item, f"{path}[{i}]"))
                return urls
            
            urls_found = find_urls(data)
            if urls_found:
                print(f"\nFound {len(urls_found)} URLs in data:")
                for url in urls_found[:10]:  # Show first 10
                    print(f"  {url}")
        
        # Wait a bit to see if any dynamic loading happens
        print(f"\nWaiting 10 seconds to see if any dynamic content loads...")
        time.sleep(10)
        
        # Check if the page source changed (indicating dynamic loading)
        new_page_source = driver.page_source
        if len(new_page_source) != len(page_source):
            print(f"Page source changed! Old: {len(page_source)} chars, New: {len(new_page_source)} chars")
        else:
            print("No dynamic loading detected")
        
        input("Press Enter to close browser and continue...")
        
    except Exception as e:
        print(f"Error investigating full runs: {e}")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    print("Full Run History Investigator")
    print("=" * 40)
    investigate_full_runs()
