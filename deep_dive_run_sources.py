#!/usr/bin/env python3
"""
Deep dive into all potential run data sources
"""

import time
import json
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
import chromedriver_binary

def deep_dive_run_sources(character_name="seedhy", region="eu", server="stormscale"):
    """Deep dive into all potential run data sources"""
    
    print(f"Deep diving run sources for: {character_name}")
    
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    
    chromedriver_binary.add_chromedriver_to_path()
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        url = f"https://raider.io/characters/{region}/{server}/{character_name}/mythic-plus"
        print(f"Loading: {url}")
        
        driver.get(url)
        
        WebDriverWait(driver, 10).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
        time.sleep(8)
        
        page_source = driver.page_source
        
        # Extract RIO data
        pattern = r'window\.__RIO_INITIAL_DATA\s*=\s*"(.*?)";'
        match = re.search(pattern, page_source, re.DOTALL)
        
        if match:
            json_string = match.group(1)
            
            try:
                data = json.loads(json_string)
            except json.JSONDecodeError:
                try:
                    unescaped = json_string.replace('\\"', '"').replace('\\\\', '\\')
                    data = json.loads(unescaped)
                except json.JSONDecodeError:
                    import codecs
                    decoded = codecs.decode(json_string, 'unicode_escape')
                    data = json.loads(decoded)
            
            print("Successfully extracted RIO data!")
            
            # Save full data for inspection
            with open(f'full_rio_data_{character_name}.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"Saved full data to full_rio_data_{character_name}.json")
            
            print(f"\n" + "="*80)
            print("DEEP DIVE INTO ALL RUN DATA SOURCES")
            print("="*80)
            
            # 1. keystoneRuns detailed analysis
            keystone_runs = data.get('keystoneRuns', {})
            if keystone_runs:
                print(f"\n1. keystoneRuns analysis:")
                print(f"   Keys: {list(keystone_runs.keys())}")
                
                # Check openRuns and openRecentRuns
                open_runs = keystone_runs.get('openRuns', {})
                open_recent_runs = keystone_runs.get('openRecentRuns', {})
                
                print(f"   openRuns: {open_runs}")
                print(f"   openRecentRuns: {open_recent_runs}")
                
                # Check loading states
                loading_dungeons = keystone_runs.get('loadingDungeons', {})
                loaded_dungeons = keystone_runs.get('loadedDungeons', {})
                
                print(f"   loadingDungeons: {loading_dungeons}")
                print(f"   loadedDungeons: {loaded_dungeons}")
                
                # Analyze dungeon runs in detail
                dungeon_runs = keystone_runs.get('dungeonRuns', {})
                for category, runs in dungeon_runs.items():
                    print(f"   {category}: {len(runs)} runs")
                    if runs:
                        # Check if runs have unique IDs we can use to detect duplicates
                        first_run = runs[0]
                        if 'summary' in first_run:
                            summary = first_run['summary']
                            print(f"     Sample run ID: {summary.get('keystone_run_id', 'No ID')}")
                            print(f"     Sample logged ID: {summary.get('logged_run_id', 'No logged ID')}")
            
            # 2. keystoneRunAggregates
            run_aggregates = data.get('keystoneRunAggregates', {})
            if run_aggregates:
                print(f"\n2. keystoneRunAggregates:")
                print(f"   Keys: {list(run_aggregates.keys())}")
                
                # Look for any run counts or totals
                for key, value in run_aggregates.items():
                    if isinstance(value, dict):
                        print(f"   {key}: {list(value.keys())}")
                        # Look for any numeric values that might be run counts
                        for subkey, subvalue in value.items():
                            if isinstance(subvalue, (int, float)) and subvalue > 20:
                                print(f"     {subkey}: {subvalue} (potential run count!)")
                    elif isinstance(value, (int, float)) and value > 20:
                        print(f"   {key}: {value} (potential run count!)")
            
            # 3. characterMythicPlusRuns detailed analysis
            char_mp_runs = data.get('characterMythicPlusRuns', {})
            if char_mp_runs:
                print(f"\n3. characterMythicPlusRuns:")
                print(f"   Keys: {list(char_mp_runs.keys())}")
                
                # Check if there are any pagination or loading indicators
                for key, value in char_mp_runs.items():
                    if 'page' in key.lower() or 'total' in key.lower() or 'count' in key.lower():
                        print(f"   {key}: {value}")
                
                # Check dungeons data
                dungeons = char_mp_runs.get('dungeons', [])
                total_runs_in_dungeons = 0
                for dungeon in dungeons:
                    runs = dungeon.get('runs', [])
                    total_runs_in_dungeons += len(runs)
                    if runs:
                        dungeon_name = dungeon.get('dungeon', {}).get('name', 'Unknown')
                        print(f"   {dungeon_name}: {len(runs)} runs")
                
                print(f"   Total runs in dungeons: {total_runs_in_dungeons}")
            
            # 4. characterMythicPlusProgress detailed analysis
            char_mp_progress = data.get('characterMythicPlusProgress', {})
            if char_mp_progress:
                print(f"\n4. characterMythicPlusProgress:")
                print(f"   Keys: {list(char_mp_progress.keys())}")
                
                progress_data = char_mp_progress.get('mythicPlusProgress', {})
                for season_key, season_data in progress_data.items():
                    if 'season-tww' in season_key and season_data:
                        print(f"   Season {season_key}:")
                        
                        # Check for any run counts or totals
                        for key, value in season_data.items():
                            if 'run' in key.lower() or 'count' in key.lower() or 'total' in key.lower():
                                print(f"     {key}: {value}")
                        
                        # Check mythicPlusScores for rawRuns
                        if 'mythicPlusScores' in season_data:
                            mp_scores = season_data['mythicPlusScores']
                            for role, role_data in mp_scores.items():
                                if isinstance(role_data, dict):
                                    raw_runs = role_data.get('rawRuns', [])
                                    raw_alt_runs = role_data.get('rawAlternateRuns', [])
                                    if raw_runs or raw_alt_runs:
                                        print(f"     {role} rawRuns: {len(raw_runs)}")
                                        print(f"     {role} rawAlternateRuns: {len(raw_alt_runs)}")
            
            # 5. Look for any other data sources with large numbers
            print(f"\n5. Searching for large numeric values (potential run counts):")
            
            def find_large_numbers(obj, path="", min_value=50):
                results = []
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        current_path = f"{path}.{key}" if path else key
                        
                        if isinstance(value, (int, float)) and value >= min_value:
                            results.append({
                                'path': current_path,
                                'value': value
                            })
                        elif isinstance(value, dict):
                            results.extend(find_large_numbers(value, current_path, min_value))
                        elif isinstance(value, list) and len(value) >= min_value:
                            results.append({
                                'path': current_path,
                                'value': f"List with {len(value)} items"
                            })
                
                return results
            
            large_numbers = find_large_numbers(data)
            for item in large_numbers:
                print(f"   {item['path']}: {item['value']}")
            
            # 6. Check for any API endpoints or URLs that might load more data
            print(f"\n6. Looking for API endpoints or URLs:")
            
            def find_api_urls(obj, path=""):
                urls = []
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        current_path = f"{path}.{key}" if path else key
                        
                        if isinstance(value, str) and ('api' in value or 'characters' in value or 'runs' in value):
                            urls.append(f"{current_path}: {value}")
                        elif isinstance(value, (dict, list)):
                            urls.extend(find_api_urls(value, current_path))
                elif isinstance(obj, list):
                    for i, item in enumerate(obj):
                        urls.extend(find_api_urls(item, f"{path}[{i}]"))
                
                return urls
            
            api_urls = find_api_urls(data)
            for url in api_urls[:20]:  # Show first 20
                print(f"   {url}")
            
            return data
            
        else:
            print("Could not find RIO initial data")
            return None
            
    except Exception as e:
        print(f"Error in deep dive: {e}")
        return None
        
    finally:
        driver.quit()

if __name__ == "__main__":
    print("Deep Dive Run Sources")
    print("=" * 30)
    deep_dive_run_sources()
