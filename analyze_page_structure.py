#!/usr/bin/env python3
"""
<PERSON>ript to analyze the page structure of raider.io character pages
to understand how M+ data is structured
"""

import time
import json
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import Web<PERSON><PERSON><PERSON>ait
import chromedriver_binary
from bs4 import Beautiful<PERSON>ou<PERSON>

def analyze_character_page(character_name="<PERSON><PERSON><PERSON><PERSON>", region="eu", server="silvermoon"):
    """Analyze the structure of a character page"""
    
    print(f"Analyzing character page for: {character_name}")
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    
    chromedriver_binary.add_chromedriver_to_path()
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        url = f"https://raider.io/characters/{region}/{server}/{character_name}"
        print(f"Loading: {url}")
        
        driver.get(url)
        
        # Wait for page to load
        WebDriverWait(driver, 10).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
        
        # Wait extra time for JavaScript content
        time.sleep(8)
        
        page_source = driver.page_source
        soup = BeautifulSoup(page_source, 'html.parser')
        
        print(f"Page loaded. Content length: {len(page_source)} characters")
        
        # Save full HTML for analysis
        with open(f"full_page_{character_name}.html", "w", encoding="utf-8") as f:
            f.write(page_source)
        print(f"Saved full page HTML to full_page_{character_name}.html")
        
        # Look for JSON data in script tags
        script_tags = soup.find_all('script')
        json_data_found = []
        
        for i, script in enumerate(script_tags):
            script_content = script.get_text()
            if script_content and ('mythic' in script_content.lower() or 'score' in script_content.lower() or 'runs' in script_content.lower()):
                print(f"\nFound potential M+ data in script tag {i}:")
                print(f"Content preview: {script_content[:200]}...")
                
                # Try to extract JSON
                try:
                    # Look for JSON objects
                    json_matches = re.findall(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', script_content)
                    for j, match in enumerate(json_matches):
                        if 'mythic' in match.lower() or 'score' in match.lower():
                            try:
                                parsed = json.loads(match)
                                json_data_found.append({
                                    'script_index': i,
                                    'match_index': j,
                                    'data': parsed
                                })
                                print(f"  Found JSON data: {match[:100]}...")
                            except:
                                pass
                except Exception as e:
                    print(f"  Error parsing JSON: {e}")
        
        # Look for specific data attributes
        print("\nLooking for data attributes...")
        elements_with_data = soup.find_all(attrs={"data-react-class": True})
        for elem in elements_with_data:
            print(f"Found element with data-react-class: {elem.get('data-react-class')}")
            if elem.get('data-react-props'):
                print(f"  Props: {elem.get('data-react-props')[:100]}...")
        
        # Look for specific class patterns
        print("\nLooking for M+ related classes...")
        mythic_classes = [
            'mythic', 'score', 'rio', 'dungeon', 'keystone', 'run', 'best',
            'season', 'rating', 'character-score', 'character-mythic'
        ]
        
        for class_name in mythic_classes:
            elements = soup.find_all(class_=re.compile(class_name, re.I))
            if elements:
                print(f"Found {len(elements)} elements with class containing '{class_name}'")
                for elem in elements[:3]:  # Show first 3
                    print(f"  {elem.name}: {elem.get('class')} - {elem.get_text(strip=True)[:50]}...")
        
        # Look for specific text patterns
        print("\nLooking for score patterns in text...")
        text_content = soup.get_text()
        
        # Look for score patterns
        score_patterns = [
            r'score[:\s]*(\d+)',
            r'rating[:\s]*(\d+)',
            r'rio[:\s]*(\d+)',
            r'(\d{3,4})\s*score'
        ]
        
        for pattern in score_patterns:
            matches = re.findall(pattern, text_content, re.I)
            if matches:
                print(f"Found potential scores with pattern '{pattern}': {matches}")
        
        # Look for run/keystone patterns
        run_patterns = [
            r'(\d+)\s*runs?',
            r'(\d+)\s*keystones?',
            r'completed[:\s]*(\d+)',
            r'total[:\s]*(\d+)'
        ]
        
        for pattern in run_patterns:
            matches = re.findall(pattern, text_content, re.I)
            if matches:
                print(f"Found potential run counts with pattern '{pattern}': {matches}")
        
        # Save JSON data found
        if json_data_found:
            with open(f"json_data_{character_name}.json", "w", encoding="utf-8") as f:
                json.dump(json_data_found, f, indent=2, ensure_ascii=False)
            print(f"\nSaved extracted JSON data to json_data_{character_name}.json")
        
        return True
        
    except Exception as e:
        print(f"Error analyzing page: {e}")
        return False
        
    finally:
        driver.quit()

if __name__ == "__main__":
    print("Raider.IO Page Structure Analyzer")
    print("=" * 40)
    analyze_character_page()
