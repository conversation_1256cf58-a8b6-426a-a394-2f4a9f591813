#!/usr/bin/env python3
"""
Test script for the Raider.IO scraper
This script tests the basic functionality without running a full scrape.
"""

import urllib.parse
from scraper import RaiderIOScraper
import config

def test_url_encoding():
    """Test URL encoding for special characters"""
    print("Testing URL encoding...")
    
    scraper = RaiderIOScraper()
    
    test_cases = [
        "<PERSON><PERSON>les<PERSON>",
        "Tëstñamé", 
        "Normalname",
        "Ñ<PERSON><PERSON><PERSON>",
        "Åsb<PERSON>ørn"
    ]
    
    for name in test_cases:
        encoded = scraper.encode_character_name(name)
        url = scraper.build_character_url(name)
        print(f"  {name} -> {encoded}")
        print(f"  URL: {url}")
        print()

def test_character_loading():
    """Test loading characters from file"""
    print("Testing character file loading...")
    
    scraper = RaiderIOScraper()
    characters = scraper.load_characters_from_file()
    
    print(f"Loaded {len(characters)} characters:")
    for char in characters:
        print(f"  - {char}")

def test_config():
    """Test configuration settings"""
    print("Testing configuration...")
    
    print(f"Default region: {config.DEFAULT_REGION}")
    print(f"Default server: {config.DEFAULT_SERVER}")
    print(f"Base URL: {config.BASE_URL}")
    print(f"Headless browser: {config.HEADLESS_BROWSER}")
    print(f"Request delay: {config.REQUEST_DELAY} seconds")
    print(f"Extract data: {config.EXTRACT_DATA}")

def main():
    """Run all tests"""
    print("Raider.IO Scraper Test Suite")
    print("=" * 40)
    
    test_config()
    print("\n" + "-" * 40)
    
    test_url_encoding()
    print("-" * 40)
    
    test_character_loading()
    print("\n" + "=" * 40)
    print("All tests completed!")
    print("\nTo run the actual scraper, use: python scraper.py")

if __name__ == "__main__":
    main()
