#!/usr/bin/env python3
"""
Extract and analyze the __RIO_INITIAL_DATA from raider.io pages
"""

import json
import re
from bs4 import BeautifulSoup

def extract_rio_data(html_file):
    """Extract the RIO initial data from HTML file"""
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Look for the __RIO_INITIAL_DATA with better regex
    # The data is a JSON string that's escaped, so we need to capture everything between quotes
    pattern = r'window\.__RIO_INITIAL_DATA\s*=\s*"(.*?)";'
    match = re.search(pattern, content, re.DOTALL)

    if not match:
        # Try alternative pattern
        pattern = r'__RIO_INITIAL_DATA\s*=\s*"(.*?)"'
        match = re.search(pattern, content, re.DOTALL)
    
    if match:
        # The data is JSON-encoded as a string, so we need to decode it
        json_string = match.group(1)

        # Save raw string for debugging
        with open('raw_json_string.txt', 'w', encoding='utf-8') as f:
            f.write(json_string)
        print(f"Saved raw JSON string to raw_json_string.txt (length: {len(json_string)})")

        # Try different unescaping approaches
        try:
            # First try: decode as if it's properly escaped JSON
            data = json.loads(json_string)
        except json.JSONDecodeError:
            try:
                # Second try: manual unescape
                unescaped = json_string.replace('\\"', '"').replace('\\\\', '\\')
                data = json.loads(unescaped)
            except json.JSONDecodeError:
                try:
                    # Third try: use Python's built-in string decode
                    import codecs
                    decoded = codecs.decode(json_string, 'unicode_escape')
                    data = json.loads(decoded)
                except:
                    print("All JSON parsing attempts failed")
                    return None

        if 'data' in locals():
            print("Successfully extracted RIO initial data!")

            # Save the extracted data
            with open('rio_initial_data.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print("Saved data to rio_initial_data.json")

            # Analyze the structure
            print("\nData structure analysis:")
            print(f"Top-level keys: {list(data.keys())}")

            # Look for character-related data
            for key, value in data.items():
                if isinstance(value, dict) and value:
                    print(f"\n{key} contains:")
                    if isinstance(value, dict):
                        print(f"  Keys: {list(value.keys())[:10]}...")  # Show first 10 keys

                        # Look for mythic+ related data
                        for subkey, subvalue in value.items():
                            if 'mythic' in str(subkey).lower() or 'score' in str(subkey).lower():
                                print(f"    Found M+ related key: {subkey}")
                                if isinstance(subvalue, dict):
                                    print(f"      Subkeys: {list(subvalue.keys())[:5]}...")

            return data
        else:
            print("Failed to parse JSON with all methods")
            return None
    else:
        print("Could not find __RIO_INITIAL_DATA in the HTML")
        return None

def analyze_mythic_plus_data(data):
    """Analyze mythic plus data structure"""
    if not data:
        return
    
    print("\n" + "="*50)
    print("MYTHIC+ DATA ANALYSIS")
    print("="*50)
    
    # Recursively search for mythic+ related data
    def find_mythic_data(obj, path=""):
        results = []
        
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{path}.{key}" if path else key
                
                # Check if this key or value contains mythic+ related info
                if any(term in str(key).lower() for term in ['mythic', 'score', 'rio', 'dungeon', 'keystone', 'run']):
                    results.append({
                        'path': current_path,
                        'key': key,
                        'value_type': type(value).__name__,
                        'value_preview': str(value)[:100] if not isinstance(value, (dict, list)) else f"{type(value).__name__} with {len(value)} items"
                    })
                
                # Recurse into nested structures
                if isinstance(value, (dict, list)):
                    results.extend(find_mythic_data(value, current_path))
                    
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                current_path = f"{path}[{i}]"
                results.extend(find_mythic_data(item, current_path))
        
        return results
    
    mythic_data = find_mythic_data(data)
    
    if mythic_data:
        print(f"Found {len(mythic_data)} potential M+ related data points:")
        for item in mythic_data[:20]:  # Show first 20
            print(f"  {item['path']}: {item['value_preview']}")
    else:
        print("No obvious M+ data found in the structure")

if __name__ == "__main__":
    print("RIO Data Extractor")
    print("=" * 30)
    
    # Try to extract from the saved HTML
    html_files = ['full_page_Jdotb.html', 'full_page_Célesti.html', 'debug_page_Celesti.html']
    
    for html_file in html_files:
        try:
            print(f"\nTrying to extract from {html_file}...")
            data = extract_rio_data(html_file)
            if data:
                analyze_mythic_plus_data(data)
                break
        except FileNotFoundError:
            print(f"File {html_file} not found")
            continue
    else:
        print("Could not find any HTML files to analyze")
