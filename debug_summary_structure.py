#!/usr/bin/env python3
"""
Debug script to understand the summary structure in keystoneRuns
"""

import time
import json
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
import chromedriver_binary

def debug_summary_structure(character_name="seedhy", region="eu", server="stormscale"):
    """Debug the summary structure in keystoneRuns"""
    
    print(f"Debugging summary structure for: {character_name}")
    
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    
    chromedriver_binary.add_chromedriver_to_path()
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        url = f"https://raider.io/characters/{region}/{server}/{character_name}"
        print(f"Loading: {url}")
        
        driver.get(url)
        
        WebDriverWait(driver, 10).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
        time.sleep(8)
        
        page_source = driver.page_source
        
        # Extract RIO data
        pattern = r'window\.__RIO_INITIAL_DATA\s*=\s*"(.*?)";'
        match = re.search(pattern, page_source, re.DOTALL)
        
        if match:
            json_string = match.group(1)
            
            try:
                data = json.loads(json_string)
            except json.JSONDecodeError:
                try:
                    unescaped = json_string.replace('\\"', '"').replace('\\\\', '\\')
                    data = json.loads(unescaped)
                except json.JSONDecodeError:
                    import codecs
                    decoded = codecs.decode(json_string, 'unicode_escape')
                    data = json.loads(decoded)
            
            print("Successfully extracted RIO data!")
            
            # Look at keystoneRuns structure
            keystone_runs = data.get('keystoneRuns', {})
            if keystone_runs and 'dungeonRuns' in keystone_runs:
                dungeon_runs = keystone_runs['dungeonRuns']
                
                print(f"\nFound {len(dungeon_runs)} run categories:")
                
                for category_key, runs_list in dungeon_runs.items():
                    if isinstance(runs_list, list) and runs_list:
                        print(f"\n=== {category_key} ===")
                        print(f"Number of runs: {len(runs_list)}")
                        
                        # Show structure of first run
                        first_run = runs_list[0]
                        print(f"First run keys: {list(first_run.keys())}")
                        
                        # Check if there's a summary
                        if 'summary' in first_run:
                            summary = first_run['summary']
                            print(f"Summary keys: {list(summary.keys())}")
                            print(f"Summary sample: {json.dumps(summary, indent=2)[:500]}...")
                        else:
                            print("No summary field found")
                            print(f"Direct run data: {json.dumps(first_run, indent=2)[:500]}...")
                        
                        # Show a few more runs to see the pattern
                        if len(runs_list) > 1:
                            print(f"\nSecond run summary keys: {list(runs_list[1].get('summary', {}).keys())}")
                            if 'summary' in runs_list[1]:
                                summary2 = runs_list[1]['summary']
                                print(f"Mythic level: {summary2.get('mythicLevel', 'NOT FOUND')}")
                                print(f"Score: {summary2.get('score', 'NOT FOUND')}")
                                print(f"Dungeon: {summary2.get('dungeon', {}).get('name', 'NOT FOUND')}")
                        
                        print("-" * 50)
            
            return data
            
        else:
            print("Could not find RIO initial data")
            return None
            
    except Exception as e:
        print(f"Error debugging summary structure: {e}")
        return None
        
    finally:
        driver.quit()

if __name__ == "__main__":
    print("Summary Structure Debugger")
    print("=" * 40)
    debug_summary_structure()
