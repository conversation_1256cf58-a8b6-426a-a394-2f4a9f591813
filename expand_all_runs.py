#!/usr/bin/env python3
"""
Scraper that expands all collapsible dungeon rows to get complete run history
"""

import time
import json
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
import chromedriver_binary

def expand_all_runs(character_name="seedhy", region="eu", server="stormscale"):
    """Expand all collapsible dungeon rows to get complete run history"""
    
    print(f"Expanding all runs for: {character_name}")
    
    chrome_options = Options()
    # Don't use headless so we can see what's happening
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    chromedriver_binary.add_chromedriver_to_path()
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        url = f"https://raider.io/characters/{region}/{server}/{character_name}/mythic-plus"
        print(f"Loading: {url}")
        
        driver.get(url)
        
        WebDriverWait(driver, 15).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
        time.sleep(5)
        
        print("Page loaded. Looking for M+ runs table...")
        
        # Look for the table or container with runs
        possible_selectors = [
            "table",
            ".runs-table", 
            ".mythic-plus-runs",
            ".dungeon-runs",
            "[data-testid*='run']",
            ".collapsible",
            ".expandable",
            ".accordion"
        ]
        
        runs_container = None
        for selector in possible_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"Found {len(elements)} elements with selector: {selector}")
                    for i, elem in enumerate(elements):
                        try:
                            text = elem.text[:100]
                            if any(keyword in text.lower() for keyword in ['dungeon', 'mythic', 'keystone', 'run']):
                                print(f"  Element {i+1}: {text}...")
                                runs_container = elem
                                break
                        except:
                            pass
                if runs_container:
                    break
            except:
                continue
        
        if not runs_container:
            print("Could not find runs container. Looking for any clickable elements...")
            
            # Look for clickable elements that might expand runs
            clickable_elements = driver.find_elements(By.XPATH, "//*[@role='button' or contains(@class, 'click') or contains(@class, 'expand') or contains(@class, 'collapse')]")
            print(f"Found {len(clickable_elements)} potentially clickable elements")
            
            for i, elem in enumerate(clickable_elements[:10]):
                try:
                    text = elem.text[:50]
                    classes = elem.get_attribute('class')
                    print(f"  {i+1}. {elem.tag_name}: {text} (classes: {classes})")
                except:
                    pass
        
        # Look for collapsible/expandable elements specifically
        print("\nLooking for collapsible/expandable elements...")
        
        expandable_selectors = [
            "[aria-expanded='false']",
            ".collapsed",
            ".collapsible",
            "[data-toggle='collapse']",
            ".accordion-toggle",
            ".expand-button",
            "button[aria-expanded]",
            ".dungeon-header",
            "tr[data-toggle]",
            ".table-row[role='button']"
        ]
        
        expandable_elements = []
        for selector in expandable_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"Found {len(elements)} elements with selector: {selector}")
                    expandable_elements.extend(elements)
            except:
                continue
        
        # Remove duplicates
        unique_expandable = list(set(expandable_elements))
        print(f"Total unique expandable elements: {len(unique_expandable)}")
        
        # Try to click on expandable elements
        expanded_count = 0
        for i, elem in enumerate(unique_expandable[:20]):  # Limit to first 20 to avoid infinite loops
            try:
                # Check if element is visible and clickable
                if elem.is_displayed() and elem.is_enabled():
                    text = elem.text[:50] if elem.text else elem.get_attribute('aria-label') or elem.get_attribute('title') or f"Element {i+1}"
                    print(f"Attempting to click: {text}")
                    
                    # Scroll element into view
                    driver.execute_script("arguments[0].scrollIntoView(true);", elem)
                    time.sleep(0.5)
                    
                    # Try different click methods
                    try:
                        elem.click()
                        expanded_count += 1
                        print(f"  ✓ Clicked successfully")
                        time.sleep(1)  # Wait for content to load
                    except:
                        try:
                            # Try JavaScript click
                            driver.execute_script("arguments[0].click();", elem)
                            expanded_count += 1
                            print(f"  ✓ JavaScript click successful")
                            time.sleep(1)
                        except:
                            try:
                                # Try ActionChains click
                                ActionChains(driver).move_to_element(elem).click().perform()
                                expanded_count += 1
                                print(f"  ✓ ActionChains click successful")
                                time.sleep(1)
                            except:
                                print(f"  ✗ All click methods failed")
                else:
                    print(f"Element {i+1} not clickable (not displayed or enabled)")
                    
            except Exception as e:
                print(f"Error clicking element {i+1}: {e}")
        
        print(f"\nExpanded {expanded_count} elements")
        
        # Wait for any dynamic content to load
        print("Waiting for dynamic content to load...")
        time.sleep(5)
        
        # Now try to extract the run data again
        print("Extracting run data after expansion...")
        
        # Look for run rows in the table
        run_selectors = [
            "tr[data-run-id]",
            ".run-row",
            ".mythic-run",
            "tr:has(.dungeon)",
            "tbody tr",
            ".table-row"
        ]
        
        all_run_elements = []
        for selector in run_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"Found {len(elements)} run elements with selector: {selector}")
                    all_run_elements.extend(elements)
            except:
                continue
        
        # Remove duplicates and count unique runs
        unique_runs = list(set(all_run_elements))
        print(f"Total unique run elements found: {len(unique_runs)}")
        
        # Try to extract data from visible run elements
        extracted_runs = []
        for i, run_elem in enumerate(unique_runs[:50]):  # Limit to first 50 for testing
            try:
                run_text = run_elem.text
                if run_text and any(keyword in run_text.lower() for keyword in ['dungeon', '+', 'keystone', 'mythic']):
                    extracted_runs.append({
                        'element_index': i,
                        'text': run_text[:200],
                        'tag': run_elem.tag_name,
                        'classes': run_elem.get_attribute('class')
                    })
            except:
                pass
        
        print(f"Extracted {len(extracted_runs)} runs from visible elements:")
        for run in extracted_runs[:10]:  # Show first 10
            print(f"  {run['element_index']}: {run['text'][:100]}...")
        
        # Also check if the RIO data changed after expansion
        print("\nChecking if RIO data changed after expansion...")
        
        page_source = driver.page_source
        pattern = r'window\.__RIO_INITIAL_DATA\s*=\s*"(.*?)";'
        match = re.search(pattern, page_source, re.DOTALL)
        
        if match:
            json_string = match.group(1)
            
            try:
                data = json.loads(json_string)
            except json.JSONDecodeError:
                try:
                    unescaped = json_string.replace('\\"', '"').replace('\\\\', '\\')
                    data = json.loads(unescaped)
                except json.JSONDecodeError:
                    import codecs
                    decoded = codecs.decode(json_string, 'unicode_escape')
                    data = json.loads(decoded)
            
            # Check keystoneRuns data
            keystone_runs = data.get('keystoneRuns', {})
            if keystone_runs and 'dungeonRuns' in keystone_runs:
                dungeon_runs = keystone_runs['dungeonRuns']
                total_runs = 0
                
                for category_key, runs_list in dungeon_runs.items():
                    if isinstance(runs_list, list):
                        total_runs += len(runs_list)
                        print(f"   {category_key}: {len(runs_list)} runs")
                
                print(f"   TOTAL RUNS IN RIO DATA: {total_runs}")
            
            # Save the data after expansion
            with open(f'expanded_rio_data_{character_name}.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"Saved expanded data to expanded_rio_data_{character_name}.json")
        
        # Keep browser open for manual inspection
        input("\nPress Enter to close browser...")
        
    except Exception as e:
        print(f"Error expanding runs: {e}")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    print("Expand All Runs Scraper")
    print("=" * 30)
    expand_all_runs()
