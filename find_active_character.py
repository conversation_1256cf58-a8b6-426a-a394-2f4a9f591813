#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to find an active character with M+ data for testing
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
import chromedriver_binary
from bs4 import BeautifulSoup

def find_active_character():
    """Find an active character from the M+ leaderboards"""
    
    print("Finding active character with M+ data...")
    
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    
    chromedriver_binary.add_chromedriver_to_path()
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Go to EU M+ leaderboards to find active characters
        url = "https://raider.io/mythic-plus-rankings/season-tww-1/all/eu/leaderboards"
        print(f"Loading leaderboards: {url}")
        
        driver.get(url)
        time.sleep(8)  # Wait for page to load
        
        soup = BeautifulSoup(driver.page_source, 'html.parser')
        
        # Look for character links in the leaderboard
        character_links = []
        
        # Find all links that look like character profiles
        links = soup.find_all('a', href=True)
        for link in links:
            href = link.get('href', '')
            if '/characters/' in href and '/eu/' in href:
                character_links.append(href)
        
        if character_links:
            print(f"Found {len(character_links)} character links")
            
            # Extract character info from the first few links
            for i, link in enumerate(character_links[:5]):
                print(f"{i+1}. {link}")
                
                # Extract character name from URL
                parts = link.split('/')
                if len(parts) >= 5:
                    region = parts[-3]
                    server = parts[-2] 
                    character = parts[-1]
                    print(f"   Character: {character}, Server: {server}, Region: {region}")
            
            # Return the first character for testing
            if character_links:
                first_link = character_links[0]
                parts = first_link.split('/')
                if len(parts) >= 5:
                    return {
                        'character': parts[-1],
                        'server': parts[-2],
                        'region': parts[-3],
                        'url': f"https://raider.io{first_link}"
                    }
        
        print("No character links found in leaderboard")
        return None
        
    except Exception as e:
        print(f"Error finding active character: {e}")
        return None
        
    finally:
        driver.quit()

def test_character_data(character_info):
    """Test extracting data from the found character"""
    if not character_info:
        return
    
    print(f"\nTesting character: {character_info['character']}")
    print(f"Server: {character_info['server']}")
    print(f"Region: {character_info['region']}")
    print(f"URL: {character_info['url']}")
    
    # Update characters.txt with this character
    with open('characters.txt', 'w', encoding='utf-8') as f:
        f.write(character_info['character'])
    
    print(f"Updated characters.txt with {character_info['character']}")
    print("You can now run: python enhanced_scraper.py")

if __name__ == "__main__":
    print("Active Character Finder")
    print("=" * 30)
    
    character_info = find_active_character()
    test_character_data(character_info)
